# Orchestra Template Engine Environment Variables
# Copy this file to .env and update the values as needed

# Application Environment
ENVIRONMENT=development

# Database Configuration
# DATABASE_URL can be used instead of individual DB settings
# DATABASE_URL=postgresql://user:password@localhost:5432/orchestra
DB__HOST=localhost
DB__PORT=5432
DB__NAME=orchestra
DB__USER=postgres
DB__PASSWORD=your_password_here
DB__ECHO=false

# API Configuration
API__HOST=0.0.0.0
API__PORT=8000
API__DEBUG=false
API__RELOAD=false

# Logging Configuration
LOG__LEVEL=INFO
LOG__FILE_PATH=/var/log/orchestra/app.log

# Security Configuration
SECURITY__SECRET_KEY=your-super-secret-key-change-in-production
SECURITY__ACCESS_TOKEN_EXPIRE_MINUTES=30

# Redis Configuration (optional)
REDIS__HOST=localhost
REDIS__PORT=6379
REDIS__PASSWORD=your_redis_password_here

# Application Settings
TEMP_DIR=/tmp/orchestra
MAX_TEMPLATE_SIZE=10485760
TEMPLATE_CACHE_TTL=3600
