# markdownlint configuration for Orchestra Template Engine
# See: https://github.com/<PERSON>A<PERSON>/markdownlint/blob/main/doc/Rules.md

# Default state for all rules
default: true

# MD001/heading-increment - Heading levels should only increment by one level at a time
MD001: true

# MD003/heading-style - Heading style
MD003:
  style: "atx"

# MD004/ul-style - Unordered list style
MD004:
  style: "dash"

# MD005/list-indent - Inconsistent indentation for list items at the same level
MD005: true

# MD007/ul-indent - Unordered list indentation
MD007:
  indent: 2
  start_indented: false

# MD009/no-trailing-spaces - Trailing spaces
MD009:
  br_spaces: 2
  list_item_empty_lines: false
  strict: false

# MD010/no-hard-tabs - Hard tabs
MD010:
  code_blocks: true
  spaces_per_tab: 4

# MD011/no-reversed-links - Reversed link syntax
MD011: true

# MD012/no-multiple-blanks - Multiple consecutive blank lines
MD012:
  maximum: 2

# MD013/line-length - Line length
MD013:
  line_length: 120
  heading_line_length: 120
  code_block_line_length: 120
  code_blocks: true
  tables: true
  headings: true
  strict: false
  stern: false

# MD014/commands-show-output - Dollar signs used before commands without showing output
MD014: true

# MD018/no-missing-space-atx - No space after hash on atx style heading
MD018: true

# MD019/no-multiple-space-atx - Multiple spaces after hash on atx style heading
MD019: true

# MD020/no-missing-space-closed-atx - No space inside hashes on closed atx style heading
MD020: true

# MD021/no-multiple-space-closed-atx - Multiple spaces inside hashes on closed atx style heading
MD021: true

# MD022/blanks-around-headings - Headings should be surrounded by blank lines
MD022:
  lines_above: 1
  lines_below: 1

# MD023/heading-start-left - Headings must start at the beginning of the line
MD023: true

# MD024/no-duplicate-heading - Multiple headings with the same content
MD024:
  allow_different_nesting: true
  siblings_only: false

# MD025/single-title - Multiple top level headings in the same document
MD025:
  level: 1
  front_matter_title: "^\\s*title\\s*[:=]"

# MD026/no-trailing-punctuation - Trailing punctuation in heading
MD026:
  punctuation: ".,;:!?"

# MD027/no-multiple-space-blockquote - Multiple spaces after blockquote symbol
MD027: true

# MD028/no-blanks-blockquote - Blank line inside blockquote
MD028: true

# MD029/ol-prefix - Ordered list item prefix
MD029:
  style: "one_or_ordered"

# MD030/list-marker-space - Spaces after list markers
MD030:
  ul_single: 1
  ol_single: 1
  ul_multi: 1
  ol_multi: 1

# MD031/blanks-around-fences - Fenced code blocks should be surrounded by blank lines
MD031:
  list_items: true

# MD032/blanks-around-lists - Lists should be surrounded by blank lines
MD032: true

# MD033/no-inline-html - Inline HTML
MD033:
  allowed_elements: ["br", "sub", "sup", "kbd", "details", "summary"]

# MD034/no-bare-urls - Bare URL used
MD034: true

# MD035/hr-style - Horizontal rule style
MD035:
  style: "---"

# MD036/no-emphasis-as-heading - Emphasis used instead of a heading
MD036:
  punctuation: ".,;:!?。，；：！？"

# MD037/no-space-in-emphasis - Spaces inside emphasis markers
MD037: true

# MD038/no-space-in-code - Spaces inside code span elements
MD038: true

# MD039/no-space-in-links - Spaces inside link text
MD039: true

# MD040/fenced-code-language - Fenced code blocks should have a language specified
MD040: true

# MD041/first-line-heading - First line in file should be a top level heading
MD041:
  level: 1
  front_matter_title: "^\\s*title\\s*[:=]"

# MD042/no-empty-links - No empty links
MD042: true

# MD043/required-headings - Required heading structure
MD043: false

# MD044/proper-names - Proper names should have the correct capitalization
MD044:
  names: ["JavaScript", "TypeScript", "GitHub", "GitLab", "API", "REST", "JSON", "YAML", "SQL", "PostgreSQL", "Docker", "Kubernetes"]
  code_blocks: false

# MD045/no-alt-text - Images should have alternate text (alt text)
MD045: true

# MD046/code-block-style - Code block style
MD046:
  style: "fenced"

# MD047/single-trailing-newline - Files should end with a single newline character
MD047: true

# MD048/code-fence-style - Code fence style
MD048:
  style: "backtick"

# MD049/emphasis-style - Emphasis style should be consistent
MD049:
  style: "asterisk"

# MD050/strong-style - Strong style should be consistent
MD050:
  style: "asterisk"

# MD051/link-fragments - Link fragments should be valid
MD051: true

# MD052/reference-links-images - Reference links and images should use a label that is defined
MD052: true

# MD053/link-image-reference-definitions - Link and image reference definitions should be needed
MD053: true
