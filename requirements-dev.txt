# Development dependencies for Orchestra Template Engine
# Install with: pip install -r requirements-dev.txt

# Include base requirements
-r requirements.txt

# Pre-commit and code quality tools
pre-commit>=3.6.0

# Code formatting
black>=23.12.1
isort>=5.13.2
autoflake>=2.2.1
pyupgrade>=3.15.0

# Linting and static analysis
flake8>=7.0.0
flake8-docstrings>=1.7.0
flake8-import-order>=0.18.2
flake8-bugbear>=23.12.2
flake8-comprehensions>=3.14.0
flake8-simplify>=0.21.0
flake8-bandit>=4.1.1
flake8-pytest-style>=1.7.2
pep8-naming>=0.13.3
pylint>=3.0.3
mypy>=1.8.0

# Security
bandit[toml]>=1.7.5
safety>=2.3.5

# Documentation and markup linting
yamllint>=1.33.0
markdownlint-cli>=0.38.0

# Git and commit tools
commitizen>=3.13.0

# Type stubs
types-PyYAML>=6.0.12
types-requests>=2.31.0

# Testing
pytest>=7.4.3
pytest-cov>=4.1.0
pytest-asyncio>=0.21.1
pytest-mock>=3.12.0
coverage>=7.3.2

# Development utilities
ipython>=8.17.2
ipdb>=0.13.13
