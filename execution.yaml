appName: 'myapp'
version: '1.0.0'

metadata:
  owner: 'payments-team'
  tags: ['payments', 'api', 'oauth']
  createdAt: '2025-05-17'

spec:
  environment: 'production'
  connections:
    - name: kafka
      pattern: PR001
      inputs:
        topic_name: 'transactions'
        retention: '7d'
    - name: oauth
      pattern: PR007
      inputs:
        client_id: 'myapp-client-id'
        scopes: ['read', 'write']

  features:
    enable_sso: true
    enable_audit: true

  secrets:
    - name: oauth_secret
      source: vault
      path: '/platform/oauth/myapp'

  overrides:
    pattern:
      PR007:
        variables:
          var1: override1
          var2: override2

---
# Pattern PR007 Definition
pattern:
  name: PR007
  version: '2.1.0'
  doc: "Enables OAuth security via TYK, PingFederate, and NetOrca."

  inputs:
    - name: client_id
      type: string
      required: true
    - name: scopes
      type: list
      default: ['openid']
    - name: enable_sso
      type: boolean
      default: false

  spec:
    stack:
      - name: 'tyk'
        repo: 'https://github.com/org/tyk-template'
        variables:
          gateway_mode: 'oauth'
          var1: '{{inputs.client_id}}'
          var2: '{{inputs.scopes | join(",")}}'

      - name: 'netorca'
        repo: 'https://github.com/org/netorca'

      - name: 'ping federate'
        repo: 'https://github.com/org/ping-federate'
        condition: '{{inputs.enable_sso}} == true'


    steps:
      - name: 'register client in auth server'
        label: 'myaction'
        action: form
        alias: 'register-client'
        inputs:
          url: 'https://auth.company.com/register'
          method: POST
          payload:
            client_id: '{{inputs.client_id}}'
            scopes: '{{inputs.scopes}}'
      - name: 'run setup script'
        depends: 'myaction'
        action: script
        inputs:
          path: './scripts/setup_oauth.sh'
          args:
            - '{{outputs.register-client.name}}'
            - '{{outputs.register-client.password}}'

  outputs:
    - type: 'config'
      location: './generated/oauth-config.yaml'
    - type: 'docker-image'
      name: 'myapp-auth-proxy'
      location: 'registry.company.com/auth/myapp:latest'

  validation:
    requiredStacks: ['tyk', 'ping federate']
    forbiddenRepos: ['https://github.com/suspicious']

  hooks:
    preDeploy: './hooks/pre_deploy.sh'
    postDeploy: './hooks/post_deploy.sh'
