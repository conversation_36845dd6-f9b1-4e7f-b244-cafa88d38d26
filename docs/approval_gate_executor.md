# Approval Gate Executor

The Approval Gate Executor is a critical component of the Orchestra Template Engine that introduces controlled pauses in automation workflows, requiring manual confirmation or automated verification before continuing. This executor enables human oversight, compliance checkpoints, and integration with external approval systems.

## Overview

The Approval Gate Executor allows you to:
- Pause workflow execution pending approval
- Integrate with external approval systems (JIRA, ServiceNow, etc.)
- Implement manual approval processes
- Set timeout limits for approval decisions
- Handle approval failures and timeouts gracefully

## Key Features

- **Multiple Approval Methods**: Manual, API, file-based, database, JIRA, ServiceNow, and custom
- **Timeout Management**: Configurable timeout periods with automatic failure handling
- **Polling Mechanism**: Configurable polling intervals for checking approval status
- **Integration Support**: Built-in support for popular approval systems
- **Custom Scripts**: Support for custom approval logic via scripts
- **Audit Trail**: Comprehensive logging of approval decisions and timing

## Approval Methods

### 1. Manual Approval (`manual`)
Simple manual approval requiring human intervention.

### 2. API Approval (`api`)
Check approval status via HTTP API calls.

### 3. File Approval (`file`)
Monitor file system for approval indicators.

### 4. Database Approval (`database`)
Query database for approval status.

### 5. ServiceNow Approval (`servicenow`)
Integration with ServiceNow approval workflows.

### 6. JIRA Approval (`jira`)
Integration with JIRA ticket approval processes.

### 7. Custom Approval (`custom`)
Execute custom scripts for approval logic.

## Configuration

### Basic Structure

```python
ApprovalGateAction(
    name="deployment_approval",
    label="Deployment Approval Gate",
    type="approval_gate",
    description="Require approval before production deployment",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.MANUAL,
        message="Please approve production deployment",
        timeout_seconds=3600,  # 1 hour timeout
        poll_interval_seconds=60  # Check every minute
    )
)
```

### Input Parameters

#### Common Parameters

- **`method`** (ApprovalMethod): The approval method to use
  - Required: Yes
  - Options: `manual`, `api`, `file`, `database`, `servicenow`, `jira`, `custom`

- **`timeout_seconds`** (int, optional): Maximum time to wait for approval
  - Default: `3600` (1 hour)
  - Example: `7200` (2 hours)

- **`poll_interval_seconds`** (int, optional): How often to check for approval
  - Default: `60` (1 minute)
  - Example: `30` (30 seconds)

- **`message`** (str, optional): Message to display for manual approval
  - Default: `"Approval required to proceed"`
  - Example: `"Please review and approve the deployment to production"`

#### Method-Specific Parameters

Each approval method has additional configuration options detailed in the examples below.

## Examples

### Manual Approval

```python
# Simple manual approval
action = ApprovalGateAction(
    name="manual_approval",
    label="Manual Approval Required",
    type="approval_gate",
    description="Wait for manual approval before proceeding",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.MANUAL,
        message="🚀 Production deployment ready. Please review and approve:",
        timeout_seconds=7200,  # 2 hours
        poll_interval_seconds=30  # Check every 30 seconds
    )
)

executor = ExecutorFactory.create(action)
result = executor.execute()

if result["status"] == "approved":
    print("✅ Deployment approved, proceeding...")
else:
    print("❌ Deployment not approved or timed out")
```

### API-Based Approval

```python
# Check approval via API endpoint
action = ApprovalGateAction(
    name="api_approval",
    label="API Approval Check",
    type="approval_gate",
    description="Check approval status via API",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.API,
        api_url="https://approval.company.com/api/v1/requests/12345",
        api_method="GET",
        api_headers={
            "Authorization": "Bearer api_token_here",
            "Accept": "application/json"
        },
        api_approval_field="status",
        api_approval_value="approved",
        timeout_seconds=3600,
        poll_interval_seconds=120  # Check every 2 minutes
    )
)
```

### File-Based Approval

```python
# Monitor file system for approval
action = ApprovalGateAction(
    name="file_approval",
    label="File-Based Approval",
    type="approval_gate",
    description="Wait for approval file to be created",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.FILE,
        file_path="/tmp/deployment_approved.flag",
        timeout_seconds=1800,  # 30 minutes
        poll_interval_seconds=15  # Check every 15 seconds
    )
)

# To approve, someone would create the file:
# touch /tmp/deployment_approved.flag
```

### Database Approval

```python
# Check approval status in database
action = ApprovalGateAction(
    name="database_approval",
    label="Database Approval Check",
    type="approval_gate",
    description="Check approval status in database",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.DATABASE,
        db_connection_string="postgresql://user:pass@localhost/approvals",
        db_query="SELECT status FROM approvals WHERE request_id = %s",
        db_query_params=["deployment_123"],
        db_approval_value="approved",
        timeout_seconds=3600,
        poll_interval_seconds=60
    )
)
```

### ServiceNow Integration

```python
# ServiceNow approval workflow
action = ApprovalGateAction(
    name="servicenow_approval",
    label="ServiceNow Approval",
    type="approval_gate",
    description="Wait for ServiceNow approval",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.SERVICENOW,
        servicenow_instance="https://company.service-now.com",
        servicenow_credentials={
            "username": "api_user",
            "password": "api_password"
        },
        servicenow_table="sysapproval_approver",
        servicenow_query="sys_id=abc123def456",
        servicenow_approval_field="state",
        servicenow_approval_value="approved",
        timeout_seconds=86400,  # 24 hours
        poll_interval_seconds=300  # Check every 5 minutes
    )
)
```

### JIRA Integration

```python
# JIRA ticket approval
action = ApprovalGateAction(
    name="jira_approval",
    label="JIRA Ticket Approval",
    type="approval_gate",
    description="Wait for JIRA ticket approval",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.JIRA,
        jira_instance="https://company.atlassian.net",
        jira_credentials={
            "username": "api_user",
            "api_token": "jira_api_token"
        },
        jira_ticket_id="DEPLOY-123",
        jira_approval_status="Done",
        timeout_seconds=43200,  # 12 hours
        poll_interval_seconds=600  # Check every 10 minutes
    )
)
```

### Custom Script Approval

```python
# Custom approval logic via script
action = ApprovalGateAction(
    name="custom_approval",
    label="Custom Approval Logic",
    type="approval_gate",
    description="Use custom script for approval logic",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.CUSTOM,
        custom_script="""
        #!/bin/bash

        # Custom approval logic
        # Check multiple conditions for approval

        # Check if all tests passed
        if [ ! -f "/tmp/tests_passed.flag" ]; then
            echo "Tests not completed"
            exit 1
        fi

        # Check if security scan passed
        if [ ! -f "/tmp/security_scan_passed.flag" ]; then
            echo "Security scan not completed"
            exit 1
        fi

        # Check if deployment window is open
        HOUR=$(date +%H)
        if [ $HOUR -lt 9 ] || [ $HOUR -gt 17 ]; then
            echo "Outside deployment window (9 AM - 5 PM)"
            exit 1
        fi

        # All checks passed
        echo "All approval conditions met"
        exit 0
        """,
        custom_script_args=[],
        timeout_seconds=1800,
        poll_interval_seconds=60
    )
)
```

## Advanced Use Cases

### Multi-Stage Approval

```python
# Implement multi-stage approval process
stages = [
    {
        "name": "technical_approval",
        "message": "Technical review required for deployment",
        "timeout": 3600,
        "approvers": ["<EMAIL>"]
    },
    {
        "name": "business_approval",
        "message": "Business approval required for production deployment",
        "timeout": 7200,
        "approvers": ["<EMAIL>"]
    },
    {
        "name": "security_approval",
        "message": "Security review required for production deployment",
        "timeout": 3600,
        "approvers": ["<EMAIL>"]
    }
]

for stage in stages:
    approval_action = ApprovalGateAction(
        name=stage["name"],
        label=f"Stage: {stage['name'].replace('_', ' ').title()}",
        type="approval_gate",
        description=stage["message"],
        inputs=ApprovalGateActionInput(
            method=ApprovalMethod.API,
            api_url=f"https://approval.company.com/api/v1/stages/{stage['name']}",
            api_headers={"Authorization": "Bearer token"},
            timeout_seconds=stage["timeout"],
            poll_interval_seconds=60
        )
    )

    executor = ExecutorFactory.create(approval_action)
    result = executor.execute()

    if result["status"] != "approved":
        print(f"❌ {stage['name']} failed or timed out")
        break
    else:
        print(f"✅ {stage['name']} approved")

print("🎉 All approval stages completed!")
```

### Conditional Approval

```python
# Different approval requirements based on environment
ConditionalAction(
    name="environment_approval",
    type="conditional",
    inputs=ConditionalActionInput(
        condition_type="env_var",
        condition_expression="ENVIRONMENT=production",
        true_action={
            "name": "production_approval",
            "type": "approval_gate",
            "description": "Production deployment requires approval",
            "inputs": {
                "method": "api",
                "api_url": "https://approval.company.com/api/v1/production",
                "timeout_seconds": 7200,
                "poll_interval_seconds": 60
            }
        },
        false_action={
            "name": "auto_approve",
            "type": "script",
            "description": "Auto-approve for non-production",
            "inputs": {
                "inline": "echo 'Auto-approved for non-production environment'",
                "shell": "bash"
            }
        }
    )
)
```

### Time-Based Approval

```python
# Approval with business hours validation
action = ApprovalGateAction(
    name="business_hours_approval",
    label="Business Hours Approval",
    type="approval_gate",
    description="Approval required during business hours",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.CUSTOM,
        custom_script="""
        #!/bin/bash

        # Check if current time is within business hours
        HOUR=$(date +%H)
        DAY=$(date +%u)  # 1=Monday, 7=Sunday

        # Business hours: Monday-Friday, 9 AM - 5 PM
        if [ $DAY -ge 1 ] && [ $DAY -le 5 ] && [ $HOUR -ge 9 ] && [ $HOUR -lt 17 ]; then
            # Check for manual approval file during business hours
            if [ -f "/tmp/manual_approval.flag" ]; then
                echo "Manual approval granted during business hours"
                exit 0
            else
                echo "Waiting for manual approval during business hours"
                exit 1
            fi
        else
            echo "Outside business hours - auto-approved"
            exit 0
        fi
        """,
        timeout_seconds=28800,  # 8 hours
        poll_interval_seconds=300  # Check every 5 minutes
    )
)
```

## Output Format

The Approval Gate Executor returns detailed information about the approval process:

### Approved

```python
{
    "status": "approved",
    "message": "Approval granted",
    "approval_time": "2023-12-01T14:30:00Z",
    "start_time": "2023-12-01T14:00:00Z",
    "duration_seconds": 1800,
    "method": "manual"
}
```

### Timeout

```python
{
    "status": "timeout",
    "message": "Approval timed out",
    "start_time": "2023-12-01T14:00:00Z",
    "timeout_seconds": 3600,
    "method": "api"
}
```

### Error

```python
{
    "status": "error",
    "message": "Failed to connect to approval API",
    "start_time": "2023-12-01T14:00:00Z",
    "error_details": "Connection timeout after 30 seconds"
}
```

### Output Fields

- **`status`**: Approval result (`"approved"`, `"timeout"`, `"error"`)
- **`message`**: Human-readable status message
- **`start_time`**: When the approval gate started
- **`approval_time`**: When approval was granted (if approved)
- **`duration_seconds`**: How long the approval process took
- **`timeout_seconds`**: Configured timeout value
- **`method`**: Approval method used
- **`error_details`**: Additional error information (if error occurred)

## Error Handling

### Common Scenarios

1. **Timeout**: Approval not received within the specified time limit
2. **Connection Errors**: Unable to connect to external approval systems
3. **Authentication Failures**: Invalid credentials for external systems
4. **Configuration Errors**: Invalid approval method configuration

### Best Practices

```python
# Robust approval gate with error handling
action = ApprovalGateAction(
    name="robust_approval",
    label="Robust Approval Gate",
    type="approval_gate",
    description="Approval gate with comprehensive error handling",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.API,
        api_url="https://approval.company.com/api/v1/requests/123",
        api_headers={
            "Authorization": "Bearer token",
            "Accept": "application/json",
            "User-Agent": "Orchestra-Template-Engine/1.0"
        },
        api_approval_field="status",
        api_approval_value="approved",
        timeout_seconds=3600,
        poll_interval_seconds=60
    )
)

try:
    executor = ExecutorFactory.create(action)
    result = executor.execute()

    if result["status"] == "approved":
        print("✅ Approval granted, proceeding with workflow")
    elif result["status"] == "timeout":
        print("⏰ Approval timed out, manual intervention required")
        # Handle timeout scenario
    else:
        print(f"❌ Approval failed: {result['message']}")
        # Handle error scenario

except Exception as e:
    print(f"💥 Approval gate execution failed: {str(e)}")
    # Handle execution failure
```

## Security Considerations

### Credential Management

1. **Environment Variables**: Store sensitive credentials in environment variables
2. **Encryption**: Encrypt credentials at rest
3. **Rotation**: Regularly rotate API tokens and passwords
4. **Least Privilege**: Use accounts with minimal required permissions

### Secure Configuration

```python
import os

# Secure approval gate configuration
action = ApprovalGateAction(
    name="secure_approval",
    label="Secure Approval Gate",
    type="approval_gate",
    description="Securely configured approval gate",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.API,
        api_url=os.getenv("APPROVAL_API_URL"),
        api_headers={
            "Authorization": f"Bearer {os.getenv('APPROVAL_API_TOKEN')}",
            "Accept": "application/json"
        },
        api_approval_field="status",
        api_approval_value="approved",
        timeout_seconds=int(os.getenv("APPROVAL_TIMEOUT", "3600")),
        poll_interval_seconds=int(os.getenv("APPROVAL_POLL_INTERVAL", "60"))
    )
)
```

### Audit and Compliance

```python
# Approval gate with comprehensive audit logging
action = ApprovalGateAction(
    name="audited_approval",
    label="Audited Approval Gate",
    type="approval_gate",
    description="Approval gate with audit trail",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.API,
        api_url="https://approval.company.com/api/v1/audited-requests",
        api_headers={
            "Authorization": "Bearer token",
            "X-Request-ID": "req_12345",
            "X-User-ID": "user_67890",
            "X-Workflow-ID": "workflow_abcdef"
        },
        timeout_seconds=3600,
        poll_interval_seconds=60
    )
)
```

## Integration with Other Executors

### With Conditional Executor

```python
# Conditional approval based on deployment size
ConditionalAction(
    name="size_based_approval",
    type="conditional",
    inputs=ConditionalActionInput(
        condition_type="expression",
        condition_expression="int(env_DEPLOYMENT_SIZE) > 100",
        true_action={
            "name": "large_deployment_approval",
            "type": "approval_gate",
            "description": "Large deployment requires approval",
            "inputs": {
                "method": "manual",
                "message": "Large deployment (>100 instances) requires approval",
                "timeout_seconds": 7200
            }
        },
        false_action={
            "name": "small_deployment_proceed",
            "type": "script",
            "description": "Small deployment auto-approved",
            "inputs": {
                "inline": "echo 'Small deployment auto-approved'",
                "shell": "bash"
            }
        }
    )
)
```

### With Script Executor

```python
# Pre-approval validation script
validation_script = ScriptAction(
    name="pre_approval_validation",
    type="script",
    inputs=ScriptActionInput(
        inline="""
        echo "Running pre-approval validations..."

        # Check if all tests passed
        if ! npm test; then
            echo "Tests failed - approval not recommended"
            exit 1
        fi

        # Check security scan
        if ! npm audit --audit-level high; then
            echo "Security vulnerabilities found - approval not recommended"
            exit 1
        fi

        echo "All validations passed - ready for approval"
        """,
        shell="bash"
    )
)

# Approval gate after validation
approval_gate = ApprovalGateAction(
    name="post_validation_approval",
    type="approval_gate",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.MANUAL,
        message="Validations passed. Approve deployment?",
        timeout_seconds=3600
    )
)
```

## Monitoring and Alerting

### Approval Metrics

Track important approval metrics:
- Average approval time
- Approval success rate
- Timeout frequency
- Approval method effectiveness

### Alert Configuration

```python
# Approval gate with alerting
action = ApprovalGateAction(
    name="monitored_approval",
    label="Monitored Approval Gate",
    type="approval_gate",
    description="Approval gate with monitoring and alerting",
    inputs=ApprovalGateActionInput(
        method=ApprovalMethod.API,
        api_url="https://approval.company.com/api/v1/requests/123",
        api_headers={
            "Authorization": "Bearer token",
            "X-Alert-Channel": "#deployments",
            "X-Alert-Timeout": "1800"  # Alert after 30 minutes
        },
        timeout_seconds=3600,
        poll_interval_seconds=60
    )
)
```

## Troubleshooting

### Common Issues

1. **Approval Never Received**: Check approval system connectivity and configuration
2. **Timeout Too Short**: Increase timeout for complex approval processes
3. **Polling Too Frequent**: Adjust poll interval to avoid overwhelming approval systems
4. **Authentication Failures**: Verify credentials and permissions

### Debug Mode

Enable detailed logging for troubleshooting:

```python
import logging
logging.getLogger('approval_gate').setLevel(logging.DEBUG)
```

### Testing Approval Gates

```python
# Test approval gate configuration
def test_approval_gate():
    """Test approval gate configuration and connectivity."""

    test_action = ApprovalGateAction(
        name="approval_test",
        type="approval_gate",
        inputs=ApprovalGateActionInput(
            method=ApprovalMethod.API,
            api_url="https://httpbin.org/status/200",
            timeout_seconds=30,
            poll_interval_seconds=5
        )
    )

    try:
        executor = ExecutorFactory.create(test_action)
        result = executor.execute()
        print("✅ Approval gate test completed")
        return True
    except Exception as e:
        print(f"❌ Approval gate test failed: {str(e)}")
        return False

# Run test
if test_approval_gate():
    print("Approval gate configuration is valid")
else:
    print("Fix approval gate configuration before proceeding")
```

## Conclusion

The Approval Gate Executor provides essential human oversight and compliance capabilities for automation workflows. By supporting multiple approval methods and integrating with popular approval systems, it enables organizations to maintain control and governance over critical automated processes while ensuring security, compliance, and quality standards are met.
