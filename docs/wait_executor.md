# Wait Executor

The Wait Executor is a fundamental component of the Orchestra Template Engine that pauses execution for a specified duration. This executor is essential for implementing delays in workflows, allowing external systems time to process, implementing rate limiting, and creating controlled timing in automation sequences.

## Overview

The Wait Executor allows you to:
- Pause workflow execution for specified durations
- Support multiple time units (seconds, minutes, hours, days)
- Display progress updates during long waits
- Implement safety limits and validation
- Add controlled delays for rate limiting
- Create timing-dependent workflow logic

## Key Features

- **Multiple Time Units**: Support for seconds, minutes, hours, and days
- **Progress Tracking**: Optional progress updates during long waits
- **Safety Limits**: Maximum duration validation to prevent excessive waits
- **Flexible Messaging**: Custom messages during wait periods
- **High Precision**: Accurate timing with sub-second precision
- **Interruption Support**: Framework for interruptible waits (future enhancement)
- **Comprehensive Logging**: Detailed logging of wait operations

## Time Units

### 1. Seconds (`seconds`)
Standard second-based timing for short delays.

### 2. Minutes (`minutes`)
Minute-based timing for medium-length delays.

### 3. Hours (`hours`)
Hour-based timing for long delays.

### 4. Days (`days`)
Day-based timing for very long delays.

## Configuration

### Basic Structure

```python
WaitAction(
    name="basic_wait",
    label="Basic Wait",
    type="wait",
    description="Wait for 30 seconds",
    inputs=WaitActionInput(
        duration=30,
        unit=WaitUnit.SECONDS,
        message="Waiting for system to stabilize..."
    )
)
```

### Input Parameters

#### Required Parameters

- **`duration`** (float): Duration to wait (must be positive)
  - Example: `30`, `1.5`, `0.5`

#### Optional Parameters

- **`unit`** (WaitUnit, optional): Time unit for duration
  - Default: `WaitUnit.SECONDS`
  - Options: `seconds`, `minutes`, `hours`, `days`

- **`message`** (str, optional): Message to display during wait
  - Default: `None`
  - Example: `"Waiting for deployment to complete..."`

- **`show_progress`** (bool, optional): Show progress updates during wait
  - Default: `False`
  - Example: `True`

- **`progress_interval`** (float, optional): Interval for progress updates in seconds
  - Default: `10.0`
  - Example: `5.0` (update every 5 seconds)

- **`allow_interruption`** (bool, optional): Allow interruption of wait
  - Default: `False`
  - Example: `True` (for future interrupt functionality)

- **`max_duration`** (float, optional): Maximum duration limit in seconds (safety check)
  - Default: `None`
  - Example: `3600` (1 hour maximum)

## Examples

### Basic Wait Operations

#### Simple Wait

```python
# Wait for 10 seconds
action = WaitAction(
    name="simple_wait",
    label="Simple Wait",
    type="wait",
    description="Wait for 10 seconds",
    inputs=WaitActionInput(
        duration=10,
        unit=WaitUnit.SECONDS,
        message="Waiting for 10 seconds..."
    )
)

executor = ExecutorFactory.create(action)
result = executor.execute()
print(f"Wait completed in {result['duration_actual']:.2f} seconds")
```

#### Different Time Units

```python
# Wait using different time units
wait_examples = [
    (30, WaitUnit.SECONDS, "30 seconds"),
    (2, WaitUnit.MINUTES, "2 minutes"),
    (0.5, WaitUnit.HOURS, "30 minutes"),
    (0.001, WaitUnit.DAYS, "1.44 minutes")
]

for duration, unit, description in wait_examples:
    action = WaitAction(
        name=f"wait_{unit.value}",
        label=f"Wait {description}",
        type="wait",
        description=f"Wait for {description}",
        inputs=WaitActionInput(
            duration=duration,
            unit=unit,
            message=f"Waiting {description}..."
        )
    )

    executor = ExecutorFactory.create(action)
    result = executor.execute()
    print(f"✅ {description} wait completed")
```

### Progress Tracking

#### Wait with Progress Updates

```python
# Long wait with progress updates
action = WaitAction(
    name="progress_wait",
    label="Wait with Progress",
    type="wait",
    description="Long wait with progress tracking",
    inputs=WaitActionInput(
        duration=2,
        unit=WaitUnit.MINUTES,
        message="Processing large dataset...",
        show_progress=True,
        progress_interval=15  # Update every 15 seconds
    )
)

executor = ExecutorFactory.create(action)
result = executor.execute()
print(f"Long operation completed in {result['duration_actual']:.1f} seconds")
```

#### Custom Progress Intervals

```python
# Different progress intervals for different wait lengths
def create_wait_with_smart_progress(duration_seconds):
    """Create wait with appropriate progress interval."""

    if duration_seconds < 30:
        # Short waits - no progress
        show_progress = False
        progress_interval = 10
    elif duration_seconds < 300:  # 5 minutes
        # Medium waits - update every 10 seconds
        show_progress = True
        progress_interval = 10
    else:
        # Long waits - update every 30 seconds
        show_progress = True
        progress_interval = 30

    return WaitAction(
        name="smart_progress_wait",
        label="Smart Progress Wait",
        type="wait",
        description="Wait with smart progress intervals",
        inputs=WaitActionInput(
            duration=duration_seconds,
            unit=WaitUnit.SECONDS,
            message=f"Processing for {duration_seconds} seconds...",
            show_progress=show_progress,
            progress_interval=progress_interval
        )
    )

# Example usage
wait_action = create_wait_with_smart_progress(120)  # 2 minutes
executor = ExecutorFactory.create(wait_action)
result = executor.execute()
```

### Workflow Integration

#### Deployment Workflow with Delays

```python
# Deployment workflow with strategic delays
def deployment_workflow():
    """Example deployment workflow with wait steps."""

    workflow_steps = [
        ("Preparing deployment", 5, "🔧 Preparing deployment environment..."),
        ("Uploading artifacts", 15, "📦 Uploading deployment artifacts..."),
        ("Updating configuration", 3, "⚙️ Updating system configuration..."),
        ("Restarting services", 20, "🔄 Restarting application services..."),
        ("Health check delay", 30, "🏥 Waiting for health checks..."),
        ("Cache warming", 45, "🔥 Warming up application caches..."),
        ("Final verification", 10, "✅ Running final verification...")
    ]

    print("🚀 Starting deployment workflow...")
    total_time = 0

    for step_name, duration, message in workflow_steps:
        print(f"\n📋 {step_name}")

        action = WaitAction(
            name=f"wait_{step_name.lower().replace(' ', '_')}",
            label=f"Wait for {step_name}",
            type="wait",
            description=f"Wait during {step_name}",
            inputs=WaitActionInput(
                duration=duration,
                unit=WaitUnit.SECONDS,
                message=message,
                show_progress=duration > 10,
                progress_interval=5 if duration > 10 else 10
            )
        )

        executor = ExecutorFactory.create(action)
        result = executor.execute()
        total_time += result['duration_actual']

        print(f"  ✅ {step_name} completed ({result['duration_actual']:.1f}s)")

    print(f"\n🎉 Deployment completed! Total time: {total_time:.1f} seconds")

deployment_workflow()
```

#### Rate Limiting Between API Calls

```python
# API calls with rate limiting
def rate_limited_api_calls():
    """Example of API calls with rate limiting delays."""

    api_endpoints = [
        "GET /users",
        "POST /users",
        "GET /users/123",
        "PUT /users/123",
        "DELETE /users/123",
        "GET /users/456",
        "POST /notifications"
    ]

    rate_limit_delay = 2  # 2 seconds between calls

    print("🌐 Making rate-limited API calls...")

    for i, endpoint in enumerate(api_endpoints):
        print(f"📡 Making request {i+1}/{len(api_endpoints)}: {endpoint}")

        # Simulate API call
        time.sleep(0.1)  # Simulate request processing

        # Add rate limiting delay (except for the last call)
        if i < len(api_endpoints) - 1:
            action = WaitAction(
                name=f"rate_limit_{i}",
                label="Rate Limiting Delay",
                type="wait",
                description="Rate limiting delay between API calls",
                inputs=WaitActionInput(
                    duration=rate_limit_delay,
                    unit=WaitUnit.SECONDS,
                    message=f"⏱️ Rate limiting... ({len(api_endpoints) - i - 1} calls remaining)"
                )
            )

            executor = ExecutorFactory.create(action)
            executor.execute()

    print("✅ All API calls completed with proper rate limiting")

rate_limited_api_calls()
```

### Safety and Validation

#### Wait with Safety Limits

```python
# Wait with maximum duration safety check
def safe_wait_example():
    """Example of wait with safety limits."""

    # Safe wait within limits
    safe_action = WaitAction(
        name="safe_wait",
        label="Safe Wait",
        type="wait",
        description="Wait with safety limits",
        inputs=WaitActionInput(
            duration=30,
            unit=WaitUnit.SECONDS,
            max_duration=60,  # Maximum 1 minute allowed
            message="⚡ Safe wait with limits..."
        )
    )

    executor = ExecutorFactory.create(safe_action)
    result = executor.execute()
    print(f"✅ Safe wait completed: {result['duration_actual']:.2f} seconds")

    # Example that would fail (commented out to avoid error)
    print("\nNote: The following would fail due to safety limits:")
    print("  duration=120, max_duration=60 -> Would raise ValueError")

safe_wait_example()
```

#### Environment-Based Wait Times

```python
# Different wait times based on environment
def environment_based_wait():
    """Wait times that vary by environment."""

    import os

    # Set environment for demo
    os.environ["ENVIRONMENT"] = "production"

    env = os.environ.get("ENVIRONMENT", "development")

    # Configure wait times based on environment
    wait_configs = {
        "development": (5, "🔧 Development - quick wait"),
        "staging": (15, "🧪 Staging - medium wait"),
        "production": (30, "🏭 Production - careful wait")
    }

    duration, message = wait_configs.get(env, (10, "❓ Unknown environment"))

    action = WaitAction(
        name="environment_wait",
        label="Environment-Based Wait",
        type="wait",
        description=f"Environment-specific wait for {env}",
        inputs=WaitActionInput(
            duration=duration,
            unit=WaitUnit.SECONDS,
            message=message,
            max_duration=60  # Safety limit
        )
    )

    executor = ExecutorFactory.create(action)
    result = executor.execute()

    print(f"Environment wait for {env}: {result['duration_actual']:.2f} seconds")

    # Clean up
    del os.environ["ENVIRONMENT"]

environment_based_wait()
```

### Advanced Use Cases

#### Conditional Wait Patterns

```python
# Conditional wait based on system state
def conditional_wait_pattern():
    """Example of conditional wait patterns."""

    # Simulate checking system state
    system_load = 0.8  # 80% load

    if system_load > 0.9:
        # High load - longer wait
        duration = 60
        message = "🔴 High system load - extended wait"
    elif system_load > 0.7:
        # Medium load - standard wait
        duration = 30
        message = "🟡 Medium system load - standard wait"
    else:
        # Low load - short wait
        duration = 10
        message = "🟢 Low system load - quick wait"

    action = WaitAction(
        name="conditional_wait",
        label="Conditional Wait",
        type="wait",
        description="Wait based on system conditions",
        inputs=WaitActionInput(
            duration=duration,
            unit=WaitUnit.SECONDS,
            message=message,
            show_progress=duration > 20
        )
    )

    executor = ExecutorFactory.create(action)
    result = executor.execute()

    print(f"Conditional wait completed based on {system_load*100}% load")

conditional_wait_pattern()
```

#### Retry Delay Pattern

```python
# Exponential backoff retry delays
def retry_with_exponential_backoff():
    """Example of retry pattern with exponential backoff."""

    max_retries = 5
    base_delay = 1  # Start with 1 second

    for attempt in range(max_retries):
        print(f"🔄 Attempt {attempt + 1}/{max_retries}")

        # Simulate operation that might fail
        success = attempt >= 3  # Succeed on 4th attempt

        if success:
            print("✅ Operation succeeded!")
            break
        else:
            print("❌ Operation failed")

            if attempt < max_retries - 1:  # Don't wait after last attempt
                # Calculate exponential backoff delay
                delay = base_delay * (2 ** attempt)

                action = WaitAction(
                    name=f"retry_delay_{attempt}",
                    label=f"Retry Delay {attempt + 1}",
                    type="wait",
                    description=f"Exponential backoff delay for retry {attempt + 1}",
                    inputs=WaitActionInput(
                        duration=delay,
                        unit=WaitUnit.SECONDS,
                        message=f"⏳ Retrying in {delay} seconds... (attempt {attempt + 2})"
                    )
                )

                executor = ExecutorFactory.create(action)
                executor.execute()

retry_with_exponential_backoff()
```

#### Batch Processing with Delays

```python
# Batch processing with delays between batches
def batch_processing_with_delays():
    """Example of batch processing with delays."""

    # Simulate large dataset
    total_items = 1000
    batch_size = 100
    delay_between_batches = 5  # 5 seconds between batches

    batches = [total_items // batch_size] * (total_items // batch_size)
    if total_items % batch_size:
        batches.append(total_items % batch_size)

    print(f"📊 Processing {total_items} items in {len(batches)} batches...")

    for i, batch_items in enumerate(batches):
        print(f"\n🔄 Processing batch {i + 1}/{len(batches)} ({batch_items} items)")

        # Simulate batch processing
        time.sleep(0.1)

        print(f"  ✅ Batch {i + 1} completed")

        # Add delay between batches (except after the last batch)
        if i < len(batches) - 1:
            remaining_batches = len(batches) - i - 1

            action = WaitAction(
                name=f"batch_delay_{i}",
                label=f"Batch Delay {i + 1}",
                type="wait",
                description="Delay between batch processing",
                inputs=WaitActionInput(
                    duration=delay_between_batches,
                    unit=WaitUnit.SECONDS,
                    message=f"⏸️ Cooling down... ({remaining_batches} batches remaining)"
                )
            )

            executor = ExecutorFactory.create(action)
            executor.execute()

    print(f"\n🎉 All {total_items} items processed successfully!")

batch_processing_with_delays()
```

## Output Format

The Wait Executor returns comprehensive information about the wait operation:

### Successful Wait

```python
{
    "duration_requested": 30.0,
    "duration_actual": 30.02,
    "unit": "seconds",
    "start_time": "2023-12-01T14:30:00.123456",
    "end_time": "2023-12-01T14:30:30.143456",
    "message": "Waiting for system to stabilize...",
    "interrupted": false,
    "success": true
}
```

### Failed Wait

```python
{
    "error": "Duration 120s exceeds max_duration 60s",
    "duration_requested": 120.0,
    "start_time": null,
    "interrupted": false,
    "success": false
}
```

### Output Fields

- **`duration_requested`**: Requested wait duration in seconds
- **`duration_actual`**: Actual wait duration in seconds
- **`unit`**: Time unit used for the original duration
- **`start_time`**: ISO timestamp when wait started
- **`end_time`**: ISO timestamp when wait completed
- **`message`**: Custom message displayed during wait
- **`interrupted`**: Whether the wait was interrupted
- **`success`**: Whether the wait completed successfully
- **`error`**: Error message (if wait failed)

## Progress Monitoring

### Real-Time Progress

```python
# Monitor wait progress (conceptual - for future async implementation)
action = WaitAction(
    name="monitored_wait",
    type="wait",
    inputs=WaitActionInput(
        duration=60,
        unit=WaitUnit.SECONDS,
        show_progress=True,
        progress_interval=10
    )
)

executor = ExecutorFactory.create(action)

# In a real async implementation, you could monitor progress:
# while not executor.is_complete():
#     progress = executor.get_progress()
#     print(f"Progress: {progress['progress_percent']:.1f}%")
#     time.sleep(1)

result = executor.execute()
```

### Progress Information

The executor provides progress information through the `get_progress()` method:

```python
{
    "elapsed_seconds": 25.5,
    "remaining_seconds": 34.5,
    "total_seconds": 60.0,
    "progress_percent": 42.5,
    "completed": false,
    "interrupted": false
}
```

## Error Handling

### Common Errors

1. **Invalid Duration**: Negative or zero duration values
2. **Exceeded Maximum**: Duration exceeds safety limits
3. **Invalid Units**: Unsupported time units
4. **System Interruption**: Unexpected system interruptions

### Error Prevention

```python
# Robust wait with comprehensive error handling
def robust_wait_example():
    """Example of robust wait with error handling."""

    try:
        action = WaitAction(
            name="robust_wait",
            label="Robust Wait",
            type="wait",
            description="Wait with comprehensive error handling",
            inputs=WaitActionInput(
                duration=30,
                unit=WaitUnit.SECONDS,
                max_duration=60,  # Safety limit
                message="⚡ Robust wait in progress...",
                show_progress=True,
                progress_interval=10
            )
        )

        executor = ExecutorFactory.create(action)
        result = executor.execute()

        if result["success"]:
            print(f"✅ Wait completed successfully in {result['duration_actual']:.2f}s")
        else:
            print(f"❌ Wait failed: {result.get('error', 'Unknown error')}")

    except ValueError as e:
        print(f"❌ Configuration error: {str(e)}")
    except Exception as e:
        print(f"❌ Execution error: {str(e)}")

robust_wait_example()
```

### Validation

```python
# Validate wait configuration before execution
def validate_wait_config(duration, unit, max_duration=None):
    """Validate wait configuration."""

    if duration <= 0:
        raise ValueError("Duration must be positive")

    # Convert to seconds for validation
    conversion_factors = {
        WaitUnit.SECONDS: 1,
        WaitUnit.MINUTES: 60,
        WaitUnit.HOURS: 3600,
        WaitUnit.DAYS: 86400
    }

    duration_seconds = duration * conversion_factors[unit]

    if max_duration and duration_seconds > max_duration:
        raise ValueError(f"Duration {duration_seconds}s exceeds maximum {max_duration}s")

    if duration_seconds > 86400:  # More than 24 hours
        print(f"⚠️ Warning: Very long wait duration: {duration_seconds}s")

    return True

# Example usage
try:
    validate_wait_config(30, WaitUnit.SECONDS, max_duration=60)
    print("✅ Configuration is valid")
except ValueError as e:
    print(f"❌ Invalid configuration: {str(e)}")
```

## Integration with Other Executors

### With Conditional Executor

```python
# Conditional wait based on environment
ConditionalAction(
    name="environment_wait",
    type="conditional",
    inputs=ConditionalActionInput(
        condition_type="env_var",
        condition_expression="ENVIRONMENT=production",
        true_action={
            "name": "production_wait",
            "type": "wait",
            "description": "Production safety wait",
            "inputs": {
                "duration": 60,
                "unit": "seconds",
                "message": "🏭 Production safety delay..."
            }
        },
        false_action={
            "name": "development_wait",
            "type": "wait",
            "description": "Development quick wait",
            "inputs": {
                "duration": 5,
                "unit": "seconds",
                "message": "🔧 Development quick delay..."
            }
        }
    )
)
```

### With Script Executor

```python
# Script execution with delays
script_action = ScriptAction(
    name="prepare_system",
    type="script",
    inputs=ScriptActionInput(
        inline="echo 'Preparing system...' && sleep 1",
        shell="bash"
    )
)

wait_action = WaitAction(
    name="stabilization_wait",
    type="wait",
    inputs=WaitActionInput(
        duration=10,
        unit=WaitUnit.SECONDS,
        message="⏳ Allowing system to stabilize..."
    )
)

verification_script = ScriptAction(
    name="verify_system",
    type="script",
    inputs=ScriptActionInput(
        inline="echo 'Verifying system...' && echo 'System ready!'",
        shell="bash"
    )
)
```

### With API Call Executor

```python
# API calls with delays
api_action = APICallAction(
    name="trigger_deployment",
    type="api_call",
    inputs=APICallActionInput(
        method="POST",
        url="https://api.example.com/deploy",
        headers={"Authorization": "Bearer token"}
    )
)

wait_action = WaitAction(
    name="deployment_wait",
    type="wait",
    inputs=WaitActionInput(
        duration=2,
        unit=WaitUnit.MINUTES,
        message="⏳ Waiting for deployment to complete...",
        show_progress=True,
        progress_interval=15
    )
)

status_check = APICallAction(
    name="check_deployment_status",
    type="api_call",
    inputs=APICallActionInput(
        method="GET",
        url="https://api.example.com/deploy/status"
    )
)
```

## Performance Considerations

### Optimization Tips

1. **Appropriate Granularity**: Use appropriate time units for readability
2. **Progress Updates**: Only enable progress for long waits (>30 seconds)
3. **Safety Limits**: Always set reasonable maximum durations
4. **Resource Usage**: Wait operations use minimal CPU and memory

### Best Practices

```python
# Best practices for wait operations
def wait_best_practices():
    """Demonstrate wait executor best practices."""

    # 1. Use appropriate time units
    short_wait = WaitActionInput(duration=30, unit=WaitUnit.SECONDS)  # Good
    # Avoid: duration=0.5, unit=WaitUnit.MINUTES  # Less readable

    # 2. Add progress for long waits
    long_wait = WaitActionInput(
        duration=5,
        unit=WaitUnit.MINUTES,
        show_progress=True,
        progress_interval=30  # Every 30 seconds
    )

    # 3. Always set safety limits for user input
    user_wait = WaitActionInput(
        duration=user_input_duration,
        unit=WaitUnit.SECONDS,
        max_duration=3600  # Maximum 1 hour
    )

    # 4. Use descriptive messages
    descriptive_wait = WaitActionInput(
        duration=45,
        unit=WaitUnit.SECONDS,
        message="🔄 Restarting services and warming caches..."
    )

    print("✅ Best practices demonstrated")

wait_best_practices()
```

## Security Considerations

### Input Validation

1. **Duration Limits**: Always validate duration inputs
2. **Safety Maximums**: Set reasonable maximum wait times
3. **Resource Protection**: Prevent excessive resource consumption

### Safe Configuration

```python
# Secure wait configuration
def secure_wait_config():
    """Example of secure wait configuration."""

    # Validate user inputs
    def create_safe_wait(user_duration, user_unit):
        """Create a safe wait with validation."""

        # Validate inputs
        if user_duration <= 0:
            raise ValueError("Duration must be positive")

        if user_duration > 3600:  # More than 1 hour
            raise ValueError("Duration too long for safety")

        # Convert to seconds for additional validation
        unit_multipliers = {
            "seconds": 1,
            "minutes": 60,
            "hours": 3600,
            "days": 86400
        }

        total_seconds = user_duration * unit_multipliers.get(user_unit, 1)

        if total_seconds > 7200:  # More than 2 hours
            raise ValueError("Total wait time exceeds safety limit")

        return WaitActionInput(
            duration=user_duration,
            unit=WaitUnit(user_unit),
            max_duration=7200,  # 2 hour absolute maximum
            message=f"⏳ Safe wait for {user_duration} {user_unit}..."
        )

    # Example usage
    try:
        safe_input = create_safe_wait(30, "seconds")
        print("✅ Safe wait configuration created")
    except ValueError as e:
        print(f"❌ Invalid configuration: {str(e)}")

secure_wait_config()
```

## Troubleshooting

### Common Issues

1. **Timing Accuracy**: System load can affect timing precision
2. **Long Waits**: Very long waits may be affected by system sleep/hibernation
3. **Progress Updates**: Progress logging frequency should match wait duration
4. **Memory Usage**: Long waits with frequent progress updates use more memory

### Debug Mode

Enable detailed logging for troubleshooting:

```python
import logging
logging.getLogger('wait_executor').setLevel(logging.DEBUG)
```

### Testing Wait Operations

```python
# Test wait operations
def test_wait_operations():
    """Test wait operations for reliability."""

    test_cases = [
        (0.1, WaitUnit.SECONDS, "Very short wait"),
        (1.0, WaitUnit.SECONDS, "Short wait"),
        (0.1, WaitUnit.MINUTES, "Medium wait"),
    ]

    for duration, unit, description in test_cases:
        print(f"Testing {description}...")

        try:
            action = WaitAction(
                name=f"test_{description.lower().replace(' ', '_')}",
                type="wait",
                inputs=WaitActionInput(
                    duration=duration,
                    unit=unit,
                    max_duration=60  # Safety limit
                )
            )

            start_time = time.time()
            executor = ExecutorFactory.create(action)
            result = executor.execute()
            actual_time = time.time() - start_time

            print(f"  ✅ {description} completed in {actual_time:.2f}s")

        except Exception as e:
            print(f"  ❌ {description} failed: {str(e)}")

test_wait_operations()
```

## Conclusion

The Wait Executor provides essential timing control for the Orchestra Template Engine. With support for multiple time units, progress tracking, safety validation, and comprehensive logging, it enables precise timing control in automation workflows while maintaining reliability and safety standards. Whether implementing simple delays, rate limiting, or complex timing-dependent workflows, the Wait Executor offers the flexibility and robustness needed for production automation systems.
