# Environment Config Executor

The Environment Config Executor is a comprehensive component of the Orchestra Template Engine that manages environment variables, configuration files, and environment-specific settings. This executor provides powerful capabilities for reading, writing, merging, and validating configuration data across multiple formats and environments.

## Overview

The Environment Config Executor allows you to:
- Read and write configuration files in multiple formats (JSON, YAML, INI, .env, Properties)
- Manage environment variables programmatically
- Merge configuration data from multiple sources
- Validate configurations against schemas
- Create backups and restore configurations
- Apply template variable substitution
- Handle environment-specific configurations

## Key Features

- **Multi-Format Support**: JSON, YAML, INI, .env, and Properties files
- **Environment Variables**: Read, write, and manage environment variables
- **Configuration Operations**: Read, write, merge, delete, backup, restore, validate
- **Template Substitution**: Replace variables in configuration files
- **Schema Validation**: Validate configurations against JSON schemas
- **Backup Management**: Automatic backup creation and restoration
- **Merge Strategies**: Deep merge, shallow merge, and overwrite strategies

## Supported Formats

### 1. JSON (`json`)
Standard JSON configuration files.

### 2. YAML (`yaml`, `yml`)
YAML configuration files with full YAML specification support.

### 3. INI (`ini`)
INI-style configuration files with sections and key-value pairs.

### 4. Environment (`env`)
Environment variable files (.env format).

### 5. Properties (`properties`)
Java-style properties files.

## Configuration Operations

### 1. Read (`read`)
Read configuration from files or environment variables.

### 2. Write (`write`)
Write configuration data to files or environment variables.

### 3. Merge (`merge`)
Merge configuration data from multiple sources.

### 4. Delete (`delete`)
Delete specific configuration keys or entire files.

### 5. Backup (`backup`)
Create backups of configuration files.

### 6. Restore (`restore`)
Restore configuration from backups.

### 7. Validate (`validate`)
Validate configuration against schemas.

### 8. Template (`template`)
Apply template variable substitution.

## Configuration

### Basic Structure

```python
EnvConfigAction(
    name="read_config",
    label="Read Configuration",
    type="env_config",
    description="Read application configuration",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.READ,
        config_format=ConfigFormat.JSON,
        file_path="./config/app.json"
    )
)
```

### Input Parameters

#### Required Parameters

- **`operation`** (ConfigOperation): The operation to perform
  - Options: `read`, `write`, `merge`, `delete`, `backup`, `restore`, `validate`, `template`

#### Optional Parameters

- **`config_format`** (ConfigFormat, optional): Configuration file format
  - Default: Auto-detected from file extension
  - Options: `json`, `yaml`, `yml`, `ini`, `env`, `properties`

- **`file_path`** (str, optional): Path to configuration file
  - Example: `"./config/app.json"`, `"/etc/myapp/config.yml"`

- **`config_data`** (Dict[str, Any], optional): Configuration data for write operations
  - Example: `{"database": {"host": "localhost", "port": 5432}}`

- **`env_vars`** (Dict[str, str], optional): Environment variables to set
  - Example: `{"DATABASE_URL": "postgresql://localhost/myapp"}`

- **`merge_strategy`** (MergeStrategy, optional): Strategy for merge operations
  - Default: `deep`
  - Options: `deep`, `shallow`, `overwrite`

- **`backup_suffix`** (str, optional): Suffix for backup files
  - Default: `".backup"`
  - Example: `".bak"`, `".old"`

- **`schema_path`** (str, optional): Path to JSON schema for validation
  - Example: `"./schemas/config-schema.json"`

- **`template_vars`** (Dict[str, str], optional): Variables for template substitution
  - Example: `{"app_name": "myapp", "version": "1.0.0"}`

## Examples

### Reading Configuration Files

#### JSON Configuration

```python
# Read JSON configuration
action = EnvConfigAction(
    name="read_json_config",
    label="Read JSON Config",
    type="env_config",
    description="Read application configuration from JSON",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.READ,
        config_format=ConfigFormat.JSON,
        file_path="./config/app.json"
    )
)

executor = ExecutorFactory.create(action)
result = executor.execute()
print(f"Configuration: {result['config_data']}")
```

#### YAML Configuration

```python
# Read YAML configuration
action = EnvConfigAction(
    name="read_yaml_config",
    label="Read YAML Config",
    type="env_config",
    description="Read configuration from YAML file",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.READ,
        config_format=ConfigFormat.YAML,
        file_path="./config/app.yml"
    )
)
```

#### Environment Variables

```python
# Read environment variables
action = EnvConfigAction(
    name="read_env_vars",
    label="Read Environment Variables",
    type="env_config",
    description="Read environment variables",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.READ,
        config_format=ConfigFormat.ENV,
        file_path=".env"
    )
)
```

### Writing Configuration Files

#### JSON Configuration

```python
# Write JSON configuration
config_data = {
    "database": {
        "host": "localhost",
        "port": 5432,
        "name": "myapp_production",
        "ssl": True
    },
    "redis": {
        "host": "localhost",
        "port": 6379,
        "db": 0
    },
    "logging": {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    }
}

action = EnvConfigAction(
    name="write_json_config",
    label="Write JSON Config",
    type="env_config",
    description="Write application configuration to JSON",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.WRITE,
        config_format=ConfigFormat.JSON,
        file_path="./config/production.json",
        config_data=config_data
    )
)
```

#### YAML Configuration

```python
# Write YAML configuration
config_data = {
    "server": {
        "host": "0.0.0.0",
        "port": 8080,
        "workers": 4
    },
    "features": {
        "authentication": True,
        "rate_limiting": True,
        "caching": False
    }
}

action = EnvConfigAction(
    name="write_yaml_config",
    label="Write YAML Config",
    type="env_config",
    description="Write server configuration to YAML",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.WRITE,
        config_format=ConfigFormat.YAML,
        file_path="./config/server.yml",
        config_data=config_data
    )
)
```

#### Environment Variables

```python
# Set environment variables
env_vars = {
    "DATABASE_URL": "postgresql://user:pass@localhost/myapp",
    "REDIS_URL": "redis://localhost:6379/0",
    "SECRET_KEY": "your-secret-key-here",
    "DEBUG": "false",
    "LOG_LEVEL": "INFO"
}

action = EnvConfigAction(
    name="set_env_vars",
    label="Set Environment Variables",
    type="env_config",
    description="Set application environment variables",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.WRITE,
        env_vars=env_vars
    )
)
```

### Merging Configurations

#### Deep Merge

```python
# Merge configurations with deep merge strategy
base_config = {
    "database": {
        "host": "localhost",
        "port": 5432
    },
    "logging": {
        "level": "INFO"
    }
}

override_config = {
    "database": {
        "name": "production_db",
        "ssl": True
    },
    "cache": {
        "enabled": True
    }
}

action = EnvConfigAction(
    name="merge_configs",
    label="Merge Configurations",
    type="env_config",
    description="Merge base and override configurations",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.MERGE,
        config_format=ConfigFormat.JSON,
        file_path="./config/merged.json",
        config_data=base_config,
        merge_data=override_config,
        merge_strategy=MergeStrategy.DEEP
    )
)

# Result will be:
# {
#     "database": {
#         "host": "localhost",
#         "port": 5432,
#         "name": "production_db",
#         "ssl": True
#     },
#     "logging": {
#         "level": "INFO"
#     },
#     "cache": {
#         "enabled": True
#     }
# }
```

#### Environment-Specific Merging

```python
# Merge environment-specific configurations
environments = ["base", "staging", "production"]
merged_config = {}

for env in environments:
    if env == "base":
        # Read base configuration
        action = EnvConfigAction(
            name=f"read_{env}_config",
            type="env_config",
            inputs=EnvConfigActionInput(
                operation=ConfigOperation.READ,
                file_path=f"./config/{env}.json"
            )
        )
        executor = ExecutorFactory.create(action)
        result = executor.execute()
        merged_config = result["config_data"]
    else:
        # Merge environment-specific overrides
        action = EnvConfigAction(
            name=f"merge_{env}_config",
            type="env_config",
            inputs=EnvConfigActionInput(
                operation=ConfigOperation.MERGE,
                file_path=f"./config/{env}.json",
                config_data=merged_config,
                merge_strategy=MergeStrategy.DEEP
            )
        )
        executor = ExecutorFactory.create(action)
        result = executor.execute()
        merged_config = result["config_data"]

# Write final merged configuration
final_action = EnvConfigAction(
    name="write_final_config",
    type="env_config",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.WRITE,
        file_path="./config/final.json",
        config_data=merged_config
    )
)
```

### Configuration Validation

#### Schema Validation

```python
# Define JSON schema
schema = {
    "type": "object",
    "properties": {
        "database": {
            "type": "object",
            "properties": {
                "host": {"type": "string"},
                "port": {"type": "integer", "minimum": 1, "maximum": 65535},
                "name": {"type": "string"},
                "ssl": {"type": "boolean"}
            },
            "required": ["host", "port", "name"]
        },
        "logging": {
            "type": "object",
            "properties": {
                "level": {"type": "string", "enum": ["DEBUG", "INFO", "WARNING", "ERROR"]}
            }
        }
    },
    "required": ["database"]
}

# Write schema file
schema_action = EnvConfigAction(
    name="write_schema",
    type="env_config",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.WRITE,
        config_format=ConfigFormat.JSON,
        file_path="./schemas/config-schema.json",
        config_data=schema
    )
)

# Validate configuration against schema
validate_action = EnvConfigAction(
    name="validate_config",
    label="Validate Configuration",
    type="env_config",
    description="Validate configuration against schema",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.VALIDATE,
        file_path="./config/app.json",
        schema_path="./schemas/config-schema.json"
    )
)

executor = ExecutorFactory.create(validate_action)
result = executor.execute()

if result["validation_result"]["valid"]:
    print("✅ Configuration is valid")
else:
    print(f"❌ Configuration validation failed: {result['validation_result']['errors']}")
```

### Template Substitution

#### Variable Substitution

```python
# Configuration template with variables
config_template = {
    "app": {
        "name": "{{app_name}}",
        "version": "{{app_version}}",
        "environment": "{{environment}}"
    },
    "database": {
        "url": "postgresql://{{db_user}}:{{db_password}}@{{db_host}}/{{db_name}}",
        "pool_size": "{{db_pool_size}}"
    },
    "api": {
        "base_url": "https://{{api_host}}/{{api_version}}",
        "timeout": "{{api_timeout}}"
    }
}

# Template variables
template_vars = {
    "app_name": "MyAwesome App",
    "app_version": "2.1.0",
    "environment": "production",
    "db_user": "app_user",
    "db_password": "secure_password",
    "db_host": "db.example.com",
    "db_name": "myapp_prod",
    "db_pool_size": "20",
    "api_host": "api.example.com",
    "api_version": "v2",
    "api_timeout": "30"
}

# Apply template substitution
action = EnvConfigAction(
    name="apply_template",
    label="Apply Configuration Template",
    type="env_config",
    description="Apply template variable substitution",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.TEMPLATE,
        config_format=ConfigFormat.JSON,
        file_path="./config/templated.json",
        config_data=config_template,
        template_vars=template_vars
    )
)
```

### Backup and Restore

#### Create Backup

```python
# Create backup of configuration
action = EnvConfigAction(
    name="backup_config",
    label="Backup Configuration",
    type="env_config",
    description="Create backup of configuration file",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.BACKUP,
        file_path="./config/production.json",
        backup_suffix=".backup"
    )
)

executor = ExecutorFactory.create(action)
result = executor.execute()
print(f"Backup created: {result['backup_path']}")
```

#### Restore from Backup

```python
# Restore configuration from backup
action = EnvConfigAction(
    name="restore_config",
    label="Restore Configuration",
    type="env_config",
    description="Restore configuration from backup",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.RESTORE,
        file_path="./config/production.json",
        backup_path="./config/production.json.backup"
    )
)
```

### Advanced Use Cases

#### Multi-Environment Configuration Management

```python
# Manage configurations for multiple environments
environments = {
    "development": {
        "database": {"host": "localhost", "debug": True},
        "cache": {"enabled": False},
        "logging": {"level": "DEBUG"}
    },
    "staging": {
        "database": {"host": "staging-db.example.com", "debug": False},
        "cache": {"enabled": True},
        "logging": {"level": "INFO"}
    },
    "production": {
        "database": {"host": "prod-db.example.com", "debug": False},
        "cache": {"enabled": True},
        "logging": {"level": "WARNING"}
    }
}

# Base configuration
base_config = {
    "app": {"name": "MyApp", "version": "1.0.0"},
    "database": {"port": 5432, "ssl": True},
    "cache": {"ttl": 3600},
    "logging": {"format": "json"}
}

for env_name, env_config in environments.items():
    # Merge base config with environment-specific config
    merge_action = EnvConfigAction(
        name=f"create_{env_name}_config",
        type="env_config",
        inputs=EnvConfigActionInput(
            operation=ConfigOperation.MERGE,
            config_format=ConfigFormat.JSON,
            file_path=f"./config/{env_name}.json",
            config_data=base_config,
            merge_data=env_config,
            merge_strategy=MergeStrategy.DEEP
        )
    )

    executor = ExecutorFactory.create(merge_action)
    result = executor.execute()
    print(f"✅ Created {env_name} configuration")
```

#### Configuration Migration

```python
# Migrate configuration from old format to new format
def migrate_config_v1_to_v2():
    """Migrate configuration from v1 to v2 format."""

    # Read old configuration
    read_action = EnvConfigAction(
        name="read_old_config",
        type="env_config",
        inputs=EnvConfigActionInput(
            operation=ConfigOperation.READ,
            file_path="./config/app-v1.json"
        )
    )

    executor = ExecutorFactory.create(read_action)
    old_config = executor.execute()["config_data"]

    # Transform configuration structure
    new_config = {
        "version": "2.0",
        "application": {
            "name": old_config.get("app_name", "Unknown"),
            "environment": old_config.get("env", "development")
        },
        "services": {
            "database": {
                "primary": {
                    "host": old_config.get("db_host", "localhost"),
                    "port": old_config.get("db_port", 5432),
                    "name": old_config.get("db_name", "app")
                }
            },
            "cache": {
                "redis": {
                    "host": old_config.get("redis_host", "localhost"),
                    "port": old_config.get("redis_port", 6379)
                }
            }
        }
    }

    # Write new configuration
    write_action = EnvConfigAction(
        name="write_new_config",
        type="env_config",
        inputs=EnvConfigActionInput(
            operation=ConfigOperation.WRITE,
            config_format=ConfigFormat.JSON,
            file_path="./config/app-v2.json",
            config_data=new_config
        )
    )

    executor = ExecutorFactory.create(write_action)
    executor.execute()
    print("✅ Configuration migrated from v1 to v2")

migrate_config_v1_to_v2()
```

## Output Format

The Environment Config Executor returns detailed information about the operation:

### Read Operation

```python
{
    "operation": "read",
    "file_path": "./config/app.json",
    "config_format": "json",
    "config_data": {
        "database": {"host": "localhost", "port": 5432},
        "logging": {"level": "INFO"}
    }
}
```

### Write Operation

```python
{
    "operation": "write",
    "file_path": "./config/app.json",
    "config_format": "json",
    "bytes_written": 256,
    "success": true
}
```

### Validation Operation

```python
{
    "operation": "validate",
    "file_path": "./config/app.json",
    "schema_path": "./schemas/config-schema.json",
    "validation_result": {
        "valid": true,
        "errors": []
    }
}
```

### Backup Operation

```python
{
    "operation": "backup",
    "file_path": "./config/app.json",
    "backup_path": "./config/app.json.backup",
    "backup_size": 1024,
    "success": true
}
```

## Error Handling

### Common Errors

1. **File Not Found**: Configuration file doesn't exist
2. **Permission Denied**: Insufficient permissions to read/write files
3. **Invalid Format**: Malformed configuration files
4. **Schema Validation**: Configuration doesn't match schema
5. **Merge Conflicts**: Incompatible data types during merge

### Error Prevention

```python
# Robust configuration handling with error checking
def safe_config_operation():
    """Safely perform configuration operations with error handling."""

    try:
        # Check if file exists before reading
        import os
        config_path = "./config/app.json"

        if not os.path.exists(config_path):
            print(f"⚠️ Configuration file not found: {config_path}")
            # Create default configuration
            default_config = {
                "database": {"host": "localhost", "port": 5432},
                "logging": {"level": "INFO"}
            }

            write_action = EnvConfigAction(
                name="create_default_config",
                type="env_config",
                inputs=EnvConfigActionInput(
                    operation=ConfigOperation.WRITE,
                    file_path=config_path,
                    config_data=default_config
                )
            )

            executor = ExecutorFactory.create(write_action)
            executor.execute()
            print("✅ Created default configuration")

        # Read configuration
        read_action = EnvConfigAction(
            name="read_config",
            type="env_config",
            inputs=EnvConfigActionInput(
                operation=ConfigOperation.READ,
                file_path=config_path
            )
        )

        executor = ExecutorFactory.create(read_action)
        result = executor.execute()
        print("✅ Configuration read successfully")
        return result["config_data"]

    except Exception as e:
        print(f"❌ Configuration operation failed: {str(e)}")
        return None

config = safe_config_operation()
```

## Security Considerations

### Sensitive Data

1. **Environment Variables**: Store sensitive data in environment variables
2. **File Permissions**: Set appropriate file permissions for configuration files
3. **Encryption**: Encrypt sensitive configuration data at rest
4. **Access Control**: Limit access to configuration files

### Best Practices

```python
import os
from pathlib import Path

# Secure configuration management
def secure_config_write():
    """Write configuration with security considerations."""

    config_data = {
        "database": {
            "host": os.getenv("DB_HOST", "localhost"),
            "port": int(os.getenv("DB_PORT", "5432")),
            "name": os.getenv("DB_NAME", "myapp"),
            # Don't store passwords in config files
            "password_env": "DB_PASSWORD"
        },
        "api": {
            "key_env": "API_KEY",  # Reference to environment variable
            "secret_env": "API_SECRET"
        }
    }

    config_path = "./config/secure.json"

    # Ensure directory exists with proper permissions
    config_dir = Path(config_path).parent
    config_dir.mkdir(mode=0o750, parents=True, exist_ok=True)

    action = EnvConfigAction(
        name="write_secure_config",
        type="env_config",
        inputs=EnvConfigActionInput(
            operation=ConfigOperation.WRITE,
            file_path=config_path,
            config_data=config_data
        )
    )

    executor = ExecutorFactory.create(action)
    executor.execute()

    # Set restrictive file permissions
    os.chmod(config_path, 0o640)
    print("✅ Secure configuration written with proper permissions")

secure_config_write()
```

## Integration with Other Executors

### With Conditional Executor

```python
# Different configurations based on environment
ConditionalAction(
    name="environment_config",
    type="conditional",
    inputs=ConditionalActionInput(
        condition_type="env_var",
        condition_expression="ENVIRONMENT=production",
        true_action={
            "name": "production_config",
            "type": "env_config",
            "inputs": {
                "operation": "write",
                "file_path": "./config/app.json",
                "config_data": {
                    "database": {"host": "prod-db.example.com"},
                    "logging": {"level": "WARNING"}
                }
            }
        },
        false_action={
            "name": "development_config",
            "type": "env_config",
            "inputs": {
                "operation": "write",
                "file_path": "./config/app.json",
                "config_data": {
                    "database": {"host": "localhost"},
                    "logging": {"level": "DEBUG"}
                }
            }
        }
    )
)
```

### With Script Executor

```python
# Generate configuration then run setup script
config_action = EnvConfigAction(
    name="generate_config",
    type="env_config",
    inputs=EnvConfigActionInput(
        operation=ConfigOperation.WRITE,
        file_path="./config/app.json",
        config_data={"database": {"host": "localhost"}}
    )
)

setup_script = ScriptAction(
    name="setup_app",
    type="script",
    inputs=ScriptActionInput(
        inline="""
        echo "Setting up application with configuration..."
        cat ./config/app.json
        echo "Configuration loaded successfully"
        """,
        shell="bash"
    )
)
```

## Performance Considerations

### Optimization Tips

1. **File Size**: Keep configuration files reasonably sized
2. **Caching**: Cache frequently accessed configurations
3. **Batch Operations**: Combine multiple operations when possible
4. **Format Choice**: Choose appropriate formats for your use case

### Large Configuration Handling

```python
# Handle large configurations efficiently
def process_large_config():
    """Process large configuration files efficiently."""

    # Read configuration in chunks for very large files
    action = EnvConfigAction(
        name="read_large_config",
        type="env_config",
        inputs=EnvConfigActionInput(
            operation=ConfigOperation.READ,
            file_path="./config/large-config.json"
        )
    )

    executor = ExecutorFactory.create(action)
    result = executor.execute()

    # Process configuration data efficiently
    config = result["config_data"]

    # Only modify what's necessary
    if "services" in config:
        for service_name, service_config in config["services"].items():
            if service_config.get("enabled", True):
                print(f"Processing enabled service: {service_name}")

    print("✅ Large configuration processed efficiently")

process_large_config()
```

## Troubleshooting

### Common Issues

1. **File Format Detection**: Ensure file extensions match content format
2. **Permission Issues**: Check file and directory permissions
3. **Schema Validation**: Verify schema syntax and structure
4. **Template Variables**: Ensure all template variables are provided

### Debug Mode

Enable detailed logging for troubleshooting:

```python
import logging
logging.getLogger('env_config').setLevel(logging.DEBUG)
```

### Configuration Testing

```python
# Test configuration operations
def test_config_operations():
    """Test configuration operations for reliability."""

    test_config = {
        "test": {"value": "hello world"},
        "number": 42,
        "boolean": True
    }

    test_file = "./config/test.json"

    try:
        # Test write
        write_action = EnvConfigAction(
            name="test_write",
            type="env_config",
            inputs=EnvConfigActionInput(
                operation=ConfigOperation.WRITE,
                file_path=test_file,
                config_data=test_config
            )
        )

        executor = ExecutorFactory.create(write_action)
        executor.execute()
        print("✅ Write test passed")

        # Test read
        read_action = EnvConfigAction(
            name="test_read",
            type="env_config",
            inputs=EnvConfigActionInput(
                operation=ConfigOperation.READ,
                file_path=test_file
            )
        )

        executor = ExecutorFactory.create(read_action)
        result = executor.execute()

        if result["config_data"] == test_config:
            print("✅ Read test passed")
        else:
            print("❌ Read test failed - data mismatch")

        # Cleanup
        import os
        os.remove(test_file)
        print("✅ Cleanup completed")

    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")

test_config_operations()
```

## Conclusion

The Environment Config Executor provides comprehensive configuration management capabilities for the Orchestra Template Engine. With support for multiple file formats, advanced merge strategies, validation, and template substitution, it enables sophisticated configuration workflows while maintaining security, reliability, and performance standards.
