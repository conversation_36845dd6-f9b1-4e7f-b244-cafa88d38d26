[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "orchestra-template-engine"
version = "1.0.0"
description = "Orchestra Template Engine - Automation workflow system with template rendering"
authors = [
    {name = "<PERSON>", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "fastapi>=0.103.1",
    "uvicorn>=0.23.2",
    "pydantic>=2.4.2",
    "jinja2>=3.1.2",
    "GitPython>=3.1.40",
    "httpx>=0.25.0",
    "copier>=9.0.0",
    "requests>=2.32.3",
    "PyYAML>=6.0.2",
    "mcp>=1.9.2",
    "cocoindex>=0.1.44",
    "aiohttp>=3.12.4",
    "sqlmodel>=0.0.14",
    "psycopg2-binary>=2.9.7",
    "pgvector>=0.2.4",
    "asyncpg>=0.29.0",
    "SQLAlchemy>=2.0.41",
    "pydantic-settings>=2.9.1",
    "alembic>=1.13.0",
]

[project.optional-dependencies]
dev = [
    "pre-commit>=3.6.0",
    "black>=23.12.1",
    "isort>=5.13.2",
    "flake8>=7.0.0",
    "flake8-docstrings>=1.7.0",
    "flake8-import-order>=0.18.2",
    "flake8-bugbear>=23.12.2",
    "flake8-comprehensions>=3.14.0",
    "flake8-simplify>=0.21.0",
    "flake8-bandit>=4.1.1",
    "flake8-pytest-style>=1.7.2",
    "pep8-naming>=0.13.3",
    "pylint>=3.0.3",
    "mypy>=1.8.0",
    "bandit[toml]>=1.7.5",
    "safety>=2.3.5",
    "autoflake>=2.2.1",
    "pyupgrade>=3.15.0",
    "commitizen>=3.13.0",
    "yamllint>=1.33.0",
    "markdownlint-cli>=0.38.0",
    "types-PyYAML>=6.0.12",
    "types-requests>=2.31.0",
    "pytest>=7.4.3",
    "pytest-cov>=4.1.0",
    "pytest-asyncio>=0.21.1",
    "pytest-mock>=3.12.0",
    "coverage>=7.3.2",
]

[project.urls]
Homepage = "https://github.com/yourusername/orchestra-template-engine"
Repository = "https://github.com/yourusername/orchestra-template-engine"
Documentation = "https://github.com/yourusername/orchestra-template-engine/docs"
"Bug Tracker" = "https://github.com/yourusername/orchestra-template-engine/issues"

# Black configuration
[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

# isort configuration
[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
known_first_party = ["app", "tests"]
known_third_party = ["fastapi", "pydantic", "sqlmodel", "sqlalchemy", "alembic"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

# mypy configuration
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true
show_column_numbers = true
show_error_context = true
pretty = true

# Per-module options
[[tool.mypy.overrides]]
module = [
    "mcp.*",
    "cocoindex.*",
    "copier.*",
    "pgvector.*",
    "psycopg2.*",
    "GitPython.*",
]
ignore_missing_imports = true

# bandit configuration
[tool.bandit]
exclude_dirs = ["tests", "venv", ".venv", "build", "dist"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process_args

# pytest configuration
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"

# coverage configuration
[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/.venv/*",
    "*/migrations/*",
    "*/alembic/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

# commitizen configuration
[tool.commitizen]
name = "cz_conventional_commits"
version = "1.0.0"
tag_format = "v$version"
version_files = [
    "pyproject.toml:version",
    "app/__init__.py:__version__",
]
bump_message = "bump: version $current_version → $new_version"
update_changelog_on_bump = true
changelog_file = "CHANGELOG.md"
