# Pre-commit configuration for Orchestra Template Engine
# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

repos:
  # Pre-commit hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
      - id: end-of-file-fixer
      - id: check-yaml
        args: [--allow-multiple-documents]
      - id: check-toml
      - id: check-json
      - id: check-xml
      - id: check-added-large-files
        args: [--maxkb=1000]
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-symlinks
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable
      - id: debug-statements
      - id: detect-private-key
      - id: fix-byte-order-marker
      - id: mixed-line-ending
        args: [--fix=lf]
      - id: requirements-txt-fixer

  # Black - Python code formatter
  - repo: https://github.com/psf/black
    rev: 23.12.1
    hooks:
      - id: black
        language_version: python3
        args: [--line-length=88, --target-version=py39]

  # isort - Import sorting
  - repo: https://github.com/pycqa/isort
    rev: 6.0.1
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]

  # flake8 - Python linting
  - repo: https://github.com/pycqa/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        additional_dependencies:
          - flake8-docstrings
          - flake8-import-order
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-simplify
          - flake8-bandit
          - flake8-pytest-style
          - pep8-naming

  # pylint - Python static analysis
  - repo: https://github.com/pycqa/pylint
    rev: v3.0.3
    hooks:
      - id: pylint
        args: [--rcfile=pylintrc]
        additional_dependencies:
          - sqlmodel
          - fastapi
          - pydantic
          - sqlalchemy
          - psycopg2-binary
          - alembic

  # mypy - Static type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        args: [--config-file=pyproject.toml]
        additional_dependencies:
          - types-PyYAML
          - types-requests
          - sqlmodel
          - fastapi
          - pydantic

  # bandit - Security linting
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-c, pyproject.toml]
        additional_dependencies: ["bandit[toml]"]

  # safety - Check for known security vulnerabilities
  - repo: https://github.com/Lucas-C/pre-commit-hooks-safety
    rev: v1.3.2
    hooks:
      - id: python-safety-dependencies-check
        args: [--ignore=70612]  # Ignore Jinja2 vulnerability in dev dependencies

  # autoflake - Remove unused imports and variables
  - repo: https://github.com/pycqa/autoflake
    rev: v2.2.1
    hooks:
      - id: autoflake
        args:
          - --in-place
          - --remove-all-unused-imports
          - --remove-unused-variables
          - --remove-duplicate-keys
          - --ignore-init-module-imports

  # pyupgrade - Upgrade Python syntax
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.15.0
    hooks:
      - id: pyupgrade
        args: [--py39-plus]

  # commitizen - Conventional commits
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.13.0
    hooks:
      - id: commitizen
        stages: [commit-msg]

  # yamllint - YAML linting
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.33.0
    hooks:
      - id: yamllint
        args: [-c=.yamllint.yaml]

  # markdownlint - Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.38.0
    hooks:
      - id: markdownlint
        args: [--config=.markdownlint.yaml]

# Global settings
default_stages: [pre-commit]
fail_fast: false
minimum_pre_commit_version: "3.0.0"
