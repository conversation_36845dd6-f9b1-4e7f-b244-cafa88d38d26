from db import init_db
from mcp.server.fastmcp import FastMCP
from mcp_server.tools import setup_tools


def create_server():
    """Create and configure the MCP server with all tools."""
    mcp = FastMCP("mcp-tools-server")

    init_db()
    tool_manager = setup_tools()

    # Register all tools in one go
    for method_name, method in tool_manager.get_all_tool_methods().items():
        mcp.tool()(method)

    return mcp, tool_manager


def main():
    print("Starting MCP Tools Server...")

    mcp, tool_manager = create_server()

    # Print summary
    registry = tool_manager.get_registry()
    discovered_count = len(tool_manager.get_discovered_tools())

    print(f"Registered {discovered_count} tool classes")
    print(f"Tools: {registry.list_tools()}")
    print(f"Methods: {registry.list_tool_methods()}")

    print("Starting FastMCP server...")
    mcp.run(transport="stdio")


if __name__ == "__main__":
    main()
