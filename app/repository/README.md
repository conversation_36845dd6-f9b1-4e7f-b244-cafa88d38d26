# Repository Pattern Implementation

This directory contains the repository pattern implementation for the Orchestra Template Engine, providing a clean data access layer with standardized database operations.

## Overview

The repository pattern provides:
- **Abstraction**: Clean separation between business logic and data access
- **Consistency**: Standardized CRUD operations across all models
- **Testability**: Easy to mock and test database operations
- **Maintainability**: Centralized database logic with reusable components

## Architecture

### BaseRepository

The `BaseRepository` class provides common database operations that all repositories inherit:

```python
from app.repository.base_repository import BaseRepository
from app.models.your_model import YourModel

class YourRepository(BaseRepository[YourModel]):
    model = YourModel
    
    # Add custom methods specific to your model
    def find_by_custom_field(self, value):
        return self.find_by(custom_field=value)
```

### Key Features

- **Generic Type Support**: Type-safe operations with `BaseRepository[ModelType]`
- **Automatic Session Management**: Handles database sessions and transactions
- **Error Handling**: Graceful error handling with proper cleanup
- **Pagination Support**: Built-in pagination with metadata
- **Search Capabilities**: Text search across multiple fields
- **Bulk Operations**: Efficient batch create, update, and delete operations

## Available Operations

### CREATE Operations

```python
# Create single record
workflow = repo.create({
    'name': 'My Workflow',
    'description': 'A sample workflow'
})

# Create multiple records
workflows = repo.create_many([
    {'name': 'Workflow 1', 'description': 'First workflow'},
    {'name': 'Workflow 2', 'description': 'Second workflow'}
])
```

### READ Operations

```python
# Get by ID
workflow = repo.get_by_id(workflow_id)

# Get all with pagination
workflows = repo.get_all(limit=10, offset=0)

# Find by field values
active_workflows = repo.find_by(is_active=True)

# Find single record
workflow = repo.find_one_by(name='My Workflow')

# Search across multiple fields
results = repo.search(['name', 'description'], 'automation')

# Count records
total = repo.count(is_active=True)

# Check existence
exists = repo.exists(name='My Workflow')
```

### UPDATE Operations

```python
# Update single record
updated = repo.update(workflow_id, {
    'description': 'Updated description'
})

# Update multiple records
count = repo.update_many(
    filters={'is_active': False},
    update_data={'status': 'archived'}
)
```

### DELETE Operations

```python
# Delete by ID
deleted = repo.delete(workflow_id)

# Delete multiple records
count = repo.delete_many(is_active=False)
```

### PAGINATION

```python
# Get paginated results with metadata
result = repo.get_paginated(
    page=1,
    page_size=10,
    order_by='created_at',
    order_desc=True,
    is_active=True  # Additional filters
)

# Result contains:
# {
#     'items': [...],
#     'total': 50,
#     'page': 1,
#     'page_size': 10,
#     'total_pages': 5
# }
```

## Creating Custom Repositories

### 1. Define Your Repository

```python
# app/repository/your_repository.py
from typing import List, Optional
from uuid import UUID

from ..models.your_model import YourModel
from .base_repository import BaseRepository

class YourRepository(BaseRepository[YourModel]):
    """Repository for YourModel with specific business logic."""
    
    model = YourModel
    
    def find_by_status(self, status: str) -> List[YourModel]:
        """Find records by status."""
        return self.find_by(status=status)
    
    def get_recent(self, limit: int = 10) -> List[YourModel]:
        """Get most recent records."""
        with self._get_session() as session:
            statement = (
                select(self.model)
                .order_by(self.model.created_at.desc())
                .limit(limit)
            )
            return session.exec(statement).all()
```

### 2. Update Repository __init__.py

```python
# app/repository/__init__.py
from .your_repository import YourRepository

__all__ = [
    "BaseRepository",
    "YourRepository",
    # ... other repositories
]
```

### 3. Use in Your Application

```python
from app.repository import YourRepository

# In your service or tool
repo = YourRepository()
records = repo.find_by_status('active')
```

## Model Requirements

Your models should inherit from `BaseModel` to get automatic timestamp management:

```python
# app/models/your_model.py
from sqlmodel import Field
from .base import BaseModel

class YourModel(BaseModel, table=True):
    name: str
    description: str
    status: str = Field(default='active')
    
    # BaseModel provides:
    # - id: UUID (primary key)
    # - created_at: datetime
    # - updated_at: datetime
    # - update_timestamp() method
```

## Transaction Support

For complex operations requiring transactions:

```python
def complex_operation(session, data):
    # This function receives a session as first parameter
    record1 = YourModel(**data['record1'])
    record2 = YourModel(**data['record2'])
    
    session.add(record1)
    session.add(record2)
    
    # If any operation fails, the entire transaction is rolled back
    return [record1, record2]

# Execute within transaction
result = repo.with_transaction(complex_operation, your_data)
```

## Best Practices

### 1. Keep Repositories Focused
- One repository per model
- Include only database-related operations
- Business logic belongs in services, not repositories

### 2. Use Type Hints
```python
class WorkflowRepository(BaseRepository[Workflow]):
    def find_active(self) -> List[Workflow]:
        return self.find_by(is_active=True)
```

### 3. Handle Errors Gracefully
```python
def safe_create(self, data: dict) -> Optional[Workflow]:
    try:
        return self.create(data)
    except Exception as e:
        logger.error(f"Failed to create workflow: {e}")
        return None
```

### 4. Use Pagination for Large Datasets
```python
# Instead of get_all() for large tables
def get_workflows_paginated(self, page: int = 1):
    return self.get_paginated(
        page=page,
        page_size=20,
        order_by='created_at',
        order_desc=True
    )
```

## Integration with MCP Tools

The repository pattern integrates seamlessly with MCP tools:

```python
# app/mcp_server/tools/your_tool.py
class YourTool(BaseTool):
    def __init__(self):
        super().__init__()
        self.repo = YourRepository()
    
    @tool_method(name="list_items")
    async def list_items(self) -> List[Dict[str, Any]]:
        try:
            items = self.repo.get_all()
            return [self._serialize_item(item) for item in items]
        except Exception as e:
            return [{"error": str(e)}]
```

## Testing

Repository pattern makes testing easier:

```python
# tests/test_repositories.py
def test_workflow_repository():
    repo = WorkflowRepository()
    
    # Test create
    workflow = repo.create({
        'name': 'Test Workflow',
        'description': 'Test description'
    })
    assert workflow.name == 'Test Workflow'
    
    # Test find
    found = repo.find_by_name('Test Workflow')
    assert found is not None
    assert found.id == workflow.id
```

## Example: Complete Workflow Repository

See `workflow_repository.py` for a complete example implementation that demonstrates:
- Custom finder methods
- Search functionality
- Date range queries
- Record duplication
- Integration with the base repository

This repository pattern provides a solid foundation for data access in the Orchestra Template Engine while maintaining clean separation of concerns and high testability.
