"""
base_repository.py

Base repository class providing common database operations for all repositories.

Author: <PERSON>
"""
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union, Sequence
from uuid import UUID

from sqlmodel import Session, SQLModel, select
from sqlalchemy import and_, or_, desc, asc, Row, RowMapping
from sqlalchemy.sql import Select
from sqlmodel.sql._expression_select_cls import _T

# Import with try/catch to handle different execution contexts
try:
    from db import get_engine
except ImportError:
    from app.db import get_engine

# Generic type for SQLModel classes
ModelType = TypeVar("ModelType", bound=SQLModel)


class BaseRepository(Generic[ModelType], ABC):
    """
    Abstract base repository providing common database operations.

    This repository follows the Repository pattern and provides:
    - Standard CRUD operations
    - Filtering and searching capabilities
    - Pagination support
    - Transaction management
    - Error handling

    Usage:
        class WorkflowRepository(BaseRepository[Workflow]):
            model = Workflow
    """

    # Subclasses must define the model class
    model: Type[ModelType] = None

    def __init__(self):
        """Initialize the repository."""
        if self.model is None:
            raise ValueError(f"{self.__class__.__name__} must define a 'model' class attribute")

    def _get_session(self) -> Session:
        """Get a database session."""
        engine = get_engine()
        return Session(engine)

    # CREATE operations

    def create(self, obj_data: Union[Dict[str, Any], ModelType]) -> ModelType:
        """
        Create a new record.

        Args:
            obj_data: Dictionary of data or model instance

        Returns:
            Created model instance
        """
        with self._get_session() as session:
            if isinstance(obj_data, dict):
                obj = self.model(**obj_data)
            else:
                obj = obj_data

            # Update timestamp if the model has update_timestamp method
            if hasattr(obj, 'update_timestamp'):
                obj.update_timestamp()

            session.add(obj)
            session.commit()
            session.refresh(obj)
            return obj

    def create_many(self, objects_data: List[Union[Dict[str, Any], ModelType]]) -> List[ModelType]:
        """
        Create multiple records in a single transaction.

        Args:
            objects_data: List of dictionaries or model instances

        Returns:
            List of created model instances
        """
        with self._get_session() as session:
            objects = []
            for obj_data in objects_data:
                if isinstance(obj_data, dict):
                    obj = self.model(**obj_data)
                else:
                    obj = obj_data

                # Update timestamp if the model has update_timestamp method
                if hasattr(obj, 'update_timestamp'):
                    obj.update_timestamp()

                objects.append(obj)

            session.add_all(objects)
            session.commit()

            # Refresh all objects to get their IDs
            for obj in objects:
                session.refresh(obj)

            return objects

    # READ operations

    def get_by_id(self, id: UUID) -> Optional[ModelType]:
        """
        Get a record by its ID.

        Args:
            id: UUID of the record

        Returns:
            Model instance or None if not found
        """
        with self._get_session() as session:
            return session.get(self.model, id)

    def get_all(self, limit: Optional[int] = None, offset: int = 0) -> Sequence[Row[Any] | RowMapping | Any]:
        """
        Get all records with optional pagination.

        Args:
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of model instances
        """
        with self._get_session() as session:
            statement = select(self.model).offset(offset)
            if limit:
                statement = statement.limit(limit)

            return session.exec(statement).all()

    def find_by(self, **filters) -> Sequence[Row[Any] | RowMapping | Any]:
        """
        Find records by field values.

        Args:
            **filters: Field name and value pairs

        Returns:
            List of matching model instances
        """
        with self._get_session() as session:
            statement = select(self.model)

            # Add filters
            conditions = []
            for field, value in filters.items():
                if hasattr(self.model, field):
                    conditions.append(getattr(self.model, field) == value)

            if conditions:
                statement = statement.where(and_(*conditions))

            return session.exec(statement).all()

    def find_one_by(self, **filters) -> Optional[ModelType]:
        """
        Find a single record by field values.

        Args:
            **filters: Field name and value pairs

        Returns:
            Model instance or None if not found
        """
        results = self.find_by(**filters)
        return results[0] if results else None

    def search(
        self,
        search_fields: List[str],
        search_term: str,
        limit: Optional[int] = None,
        offset: int = 0
    ) -> Sequence[Row[Any] | RowMapping | Any]:
        """
        Search records by text in specified fields.

        Args:
            search_fields: List of field names to search in
            search_term: Text to search for
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List of matching model instances
        """
        with self._get_session() as session:
            statement = select(self.model)

            # Build search conditions
            search_conditions = []
            for field in search_fields:
                if hasattr(self.model, field):
                    field_attr = getattr(self.model, field)
                    search_conditions.append(field_attr.ilike(f"%{search_term}%"))

            if search_conditions:
                statement = statement.where(or_(*search_conditions))

            statement = statement.offset(offset)
            if limit:
                statement = statement.limit(limit)

            return session.exec(statement).all()

    def count(self, **filters) -> int:
        """
        Count records matching the given filters.

        Args:
            **filters: Field name and value pairs

        Returns:
            Number of matching records
        """
        with self._get_session() as session:
            statement = select(self.model)

            # Add filters
            conditions = []
            for field, value in filters.items():
                if hasattr(self.model, field):
                    conditions.append(getattr(self.model, field) == value)

            if conditions:
                statement = statement.where(and_(*conditions))

            return len(session.exec(statement).all())

    def exists(self, **filters) -> bool:
        """
        Check if any records exist matching the given filters.

        Args:
            **filters: Field name and value pairs

        Returns:
            True if at least one record exists, False otherwise
        """
        return self.count(**filters) > 0

    # UPDATE operations

    def update(self, id: UUID, update_data: Dict[str, Any]) -> Optional[ModelType]:
        """
        Update a record by ID.

        Args:
            id: UUID of the record to update
            update_data: Dictionary of fields to update

        Returns:
            Updated model instance or None if not found
        """
        with self._get_session() as session:
            obj = session.get(self.model, id)
            if not obj:
                return None

            # Update fields
            for field, value in update_data.items():
                if hasattr(obj, field):
                    setattr(obj, field, value)

            # Update timestamp if the model has update_timestamp method
            if hasattr(obj, 'update_timestamp'):
                obj.update_timestamp()

            session.add(obj)
            session.commit()
            session.refresh(obj)
            return obj

    def update_many(self, filters: Dict[str, Any], update_data: Dict[str, Any]) -> int:
        """
        Update multiple records matching the given filters.

        Args:
            filters: Field name and value pairs to filter records
            update_data: Dictionary of fields to update

        Returns:
            Number of updated records
        """
        with self._get_session() as session:
            # Find matching records
            statement = select(self.model)
            conditions = []
            for field, value in filters.items():
                if hasattr(self.model, field):
                    conditions.append(getattr(self.model, field) == value)

            if conditions:
                statement = statement.where(and_(*conditions))

            objects = session.exec(statement).all()

            # Update each object
            for obj in objects:
                for field, value in update_data.items():
                    if hasattr(obj, field):
                        setattr(obj, field, value)

                # Update timestamp if the model has update_timestamp method
                if hasattr(obj, 'update_timestamp'):
                    obj.update_timestamp()

                session.add(obj)

            session.commit()
            return len(objects)

    # DELETE operations

    def delete(self, id: UUID) -> bool:
        """
        Delete a record by ID.

        Args:
            id: UUID of the record to delete

        Returns:
            True if deleted, False if not found
        """
        with self._get_session() as session:
            obj = session.get(self.model, id)
            if not obj:
                return False

            session.delete(obj)
            session.commit()
            return True

    def delete_many(self, **filters) -> int:
        """
        Delete multiple records matching the given filters.

        Args:
            **filters: Field name and value pairs

        Returns:
            Number of deleted records
        """
        with self._get_session() as session:
            # Find matching records
            statement = select(self.model)
            conditions = []
            for field, value in filters.items():
                if hasattr(self.model, field):
                    conditions.append(getattr(self.model, field) == value)

            if conditions:
                statement = statement.where(and_(*conditions))

            objects = session.exec(statement).all()

            # Delete each object
            for obj in objects:
                session.delete(obj)

            session.commit()
            return len(objects)

    # UTILITY methods

    def get_paginated(
        self,
        page: int = 1,
        page_size: int = 10,
        order_by: Optional[str] = None,
        order_desc: bool = False,
        **filters
    ) -> Dict[str, Any]:
        """
        Get paginated results with metadata.

        Args:
            page: Page number (1-based)
            page_size: Number of records per page
            order_by: Field name to order by
            order_desc: Whether to order in descending order
            **filters: Field name and value pairs for filtering

        Returns:
            Dictionary with 'items', 'total', 'page', 'page_size', 'total_pages'
        """
        with self._get_session() as session:
            # Build base query
            statement = select(self.model)

            # Add filters
            conditions = []
            for field, value in filters.items():
                if hasattr(self.model, field):
                    conditions.append(getattr(self.model, field) == value)

            if conditions:
                statement = statement.where(and_(*conditions))

            # Get total count
            total = len(session.exec(statement).all())

            # Add ordering
            if order_by and hasattr(self.model, order_by):
                order_field = getattr(self.model, order_by)
                if order_desc:
                    statement = statement.order_by(desc(order_field))
                else:
                    statement = statement.order_by(asc(order_field))

            # Add pagination
            offset = (page - 1) * page_size
            statement = statement.offset(offset).limit(page_size)

            items = session.exec(statement).all()

            total_pages = (total + page_size - 1) // page_size

            return {
                'items': items,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': total_pages
            }

    def execute_raw_query(self, statement: Select) -> Sequence[_T]:
        """
        Execute a raw SQLAlchemy select statement.

        Args:
            statement: SQLAlchemy Select statement

        Returns:
            List of model instances
        """
        with self._get_session() as session:
            return session.exec(statement).all()

    # Transaction support

    def with_transaction(self, func, *args, **kwargs):
        """
        Execute a function within a database transaction.

        Args:
            func: Function to execute
            *args: Arguments to pass to the function
            **kwargs: Keyword arguments to pass to the function

        Returns:
            Result of the function
        """
        with self._get_session() as session:
            try:
                # Pass session as first argument to the function
                result = func(session, *args, **kwargs)
                session.commit()
                return result
            except Exception:
                session.rollback()
                raise
