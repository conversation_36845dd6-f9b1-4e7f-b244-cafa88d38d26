"""
workflow_repository.py

Repository for Workflow model operations.

Author: <PERSON>
"""
from typing import List, Optional
from uuid import UUID

from sqlmodel import select

# Import with try/catch to handle different execution contexts
try:
    from models.workflow import Workflow
except ImportError:
    from app.models.workflow import Workflow
from .base_repository import BaseRepository


class WorkflowRepository(BaseRepository[Workflow]):
    """Repository for Workflow model with specific business logic."""

    model = Workflow

    def find_by_name(self, name: str) -> Optional[Workflow]:
        """
        Find a workflow by its name.

        Args:
            name: Workflow name

        Returns:
            Workflow instance or None if not found
        """
        return self.find_one_by(name=name)

    def find_active_workflows(self) -> List[Workflow]:
        """
        Find all active workflows.

        Note: This assumes the Workflow model will have an 'is_active' field.
        Currently the model doesn't have this field, so this is a placeholder.

        Returns:
            List of active workflows
        """
        # This would work if the model had an is_active field
        # return self.find_by(is_active=True)

        # For now, return all workflows
        return self.get_all()

    def search_workflows(self, search_term: str, limit: Optional[int] = None) -> List[Workflow]:
        """
        Search workflows by name and description.

        Args:
            search_term: Text to search for
            limit: Maximum number of results

        Returns:
            List of matching workflows
        """
        return self.search(
            search_fields=['name', 'description'],
            search_term=search_term,
            limit=limit
        )

    def get_workflows_by_date_range(
        self,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> List[Workflow]:
        """
        Get workflows created within a date range.

        Args:
            start_date: Start date in ISO format
            end_date: End date in ISO format

        Returns:
            List of workflows in the date range
        """
        with self._get_session() as session:
            statement = select(self.model)

            conditions = []
            if start_date:
                conditions.append(self.model.created_at >= start_date)
            if end_date:
                conditions.append(self.model.created_at <= end_date)

            if conditions:
                from sqlalchemy import and_
                statement = statement.where(and_(*conditions))

            return session.exec(statement).all()

    def get_recent_workflows(self, limit: int = 10) -> List[Workflow]:
        """
        Get the most recently created workflows.

        Args:
            limit: Maximum number of workflows to return

        Returns:
            List of recent workflows
        """
        with self._get_session() as session:
            statement = (
                select(self.model)
                .order_by(self.model.created_at.desc())
                .limit(limit)
            )
            return session.exec(statement).all()

    def duplicate_workflow(self, workflow_id: UUID, new_name: str) -> Workflow:
        """
        Create a duplicate of an existing workflow with a new name.

        Args:
            workflow_id: ID of the workflow to duplicate
            new_name: Name for the new workflow

        Returns:
            New workflow instance

        Raises:
            ValueError: If the original workflow is not found
        """
        original = self.get_by_id(workflow_id)
        if not original:
            raise ValueError(f"Workflow with ID {workflow_id} not found")

        # Create new workflow with same data but different name
        new_workflow_data = {
            'name': new_name,
            'description': f"Copy of {original.description}"
        }

        return self.create(new_workflow_data)
