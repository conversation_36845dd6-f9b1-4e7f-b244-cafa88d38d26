"""
main.py

Main FastAPI application with configuration-based setup.

Author: <PERSON>
"""
import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app import db
from app.settings import (
    get_api_config,
    get_logging_config,
    get_security_config,
    get_settings,
)


def setup_logging():
    """Configure application logging based on settings."""
    log_config = get_logging_config()

    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_config.level), format=log_config.format
    )

    # Configure file logging if specified
    if log_config.file_path:
        import os
        from logging.handlers import RotatingFileHandler

        # Create log directory if it doesn't exist
        log_dir = os.path.dirname(log_config.file_path)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)

        # Add file handler
        file_handler = RotatingFileHandler(
            log_config.file_path,
            maxBytes=log_config.max_file_size,
            backupCount=log_config.backup_count,
        )
        file_handler.setFormatter(logging.Formatter(log_config.format))

        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management."""
    # Startup
    setup_logging()
    logger = logging.getLogger(__name__)

    settings = get_settings()
    logger.info(f"Starting Orchestra Template Engine in {settings.environment} mode")
    logger.info(f"Database URL: {db.get_database_url()}")

    # Initialize database
    db.init_db()
    logger.info("Database initialized")

    yield

    # Shutdown
    logger.info("Shutting down Orchestra Template Engine")
    db.reset_engine()


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    # Get configuration
    api_config = get_api_config()
    security_config = get_security_config()

    # Create FastAPI app with configuration
    app = FastAPI(
        title=api_config.title,
        description=api_config.description,
        version=api_config.version,
        debug=api_config.debug,
        lifespan=lifespan,
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=security_config.cors_origins,
        allow_credentials=True,
        allow_methods=security_config.cors_methods,
        allow_headers=security_config.cors_headers,
    )

    return app


# Create the app instance
app = create_app()
