"""
workflow_tool.py

Author: <PERSON>
"""
from typing import List, Dict, Any
# Use only the server context imports (the way server.py does it)
from db import get_engine
from models.workflow import Workflow
from sqlmodel import Session, select

from .base_tool import BaseTool, tool_method



class WorkflowTool(BaseTool):
    """Tool for interacting with workflows."""

    @property
    def tool_name(self) -> str:
        return "workflow_tool"

    @property
    def tool_description(self) -> str:
        return "Manage and interact with automation workflows"

    @tool_method(name="list_workflows")
    async def list_workflows(self) -> List[Dict[str, Any]]:
        """List all workflows."""
        try:

            engine = get_engine()
            with Session(engine) as session:
                statement = select(Workflow)
                workflows = session.exec(statement).all()

                # Convert SQLModel objects to dictionaries for serialization
                workflow_list = []
                for workflow in workflows:
                    workflow_dict = {
                        "id": str(workflow.id),
                        "name": workflow.name,
                        "description": workflow.description,
                        "created_at": workflow.created_at.isoformat() if workflow.created_at else None
                    }
                    workflow_list.append(workflow_dict)

                return workflow_list
        except Exception as e:
            return [{"error": f"Failed to retrieve workflows: {str(e)}"}]
