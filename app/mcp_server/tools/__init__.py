"""
__init__.py

MCP Tools Package - Auto-discovery and registration of all tools.

Author: <PERSON>
"""

import importlib
import inspect
import os
from typing import List, Type

from .base_tool import BaseTool, ToolRegistry, get_tool_registry, tool_method


class ToolManager:
    """
    Manages automatic discovery and registration of all tool classes.

    This is the single entry point for tool management.
    """

    def __init__(self):
        self._registry = get_tool_registry()
        self._discovered_tools: list[type[BaseTool]] = []

    def discover_and_register_all_tools(self):
        """
        Automatically discover all tool classes in the tools directory and register them.

        This method:
        1. Scans all Python files in the tools directory
        2. Finds classes that inherit from BaseTool
        3. Registers them with the global registry
        """
        tools_dir = os.path.dirname(__file__)

        # Get all Python files in the tools directory (excluding __init__.py and base_tool.py)
        python_files = [
            f[:-3]
            for f in os.listdir(tools_dir)
            if f.endswith(".py") and f not in ["__init__.py", "base_tool.py"]
        ]

        for module_name in python_files:
            try:
                # Import the module
                module = importlib.import_module(f".{module_name}", package=__name__)

                # Find all classes in the module that inherit from BaseTool
                for name, obj in inspect.getmembers(module, inspect.isclass):
                    if (
                        issubclass(obj, BaseTool)
                        and obj is not BaseTool
                        and obj.__module__ == module.__name__
                    ):
                        print(f"Discovered tool class: {name} from {module_name}")
                        self._discovered_tools.append(obj)

                        # Register the tool class
                        self._registry.register_tool_class(obj)
                        print(f"Registered tool: {obj().tool_name}")

            except Exception as e:
                print(f"Warning: Failed to import or process module {module_name}: {e}")
                continue

    def get_registry(self) -> ToolRegistry:
        """Get the tool registry."""
        return self._registry

    def get_discovered_tools(self) -> list[type[BaseTool]]:
        """Get the list of discovered tool classes."""
        return self._discovered_tools

    def get_all_tool_methods(self):
        """Get all registered tool methods."""
        return self._registry.get_all_tool_methods()


# Create a global tool manager instance
_tool_manager = ToolManager()


def setup_tools():
    """
    Single function to discover and register all tools.

    This is the ONLY function you need to import and call.
    """
    _tool_manager.discover_and_register_all_tools()
    return _tool_manager


# Export only what's needed
__all__ = [
    "setup_tools",  # The single entry point
    "BaseTool",  # For creating new tools
    "tool_method",  # For decorating tool methods
]
