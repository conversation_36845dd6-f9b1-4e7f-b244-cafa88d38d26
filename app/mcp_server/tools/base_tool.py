"""
base_tool.py

Base class for all MCP tools with automatic registration.

Author: <PERSON>
"""

import inspect
from abc import ABC, abstractmethod
from functools import wraps
from typing import Callable, Optional


class BaseTool(ABC):
    """
    Abstract base class for all MCP tools.

    Provides automatic tool registration and common functionality.
    Tools that inherit from this class will be automatically registered
    when the class is instantiated.
    """

    def __init__(self):
        """Initialize the tool and register its methods."""
        self._tool_methods: dict[str, Callable] = {}
        self._register_tool_methods()

    @property
    @abstractmethod
    def tool_name(self) -> str:
        """Return the name of this tool."""

    @property
    @abstractmethod
    def tool_description(self) -> str:
        """Return the description of this tool."""

    def _register_tool_methods(self):
        """
        Automatically discover and register methods marked as tools.

        Methods decorated with @tool_method will be registered as callable tools.
        """
        for name, method in inspect.getmembers(self, predicate=inspect.ismethod):
            if hasattr(method, "_is_tool_method"):
                tool_name = getattr(method, "_tool_name", name)
                self._tool_methods[tool_name] = method

    def get_tool_methods(self) -> dict[str, Callable]:
        """Return all registered tool methods."""
        return self._tool_methods.copy()

    def get_tool_method(self, name: str) -> Optional[Callable]:
        """Get a specific tool method by name."""
        return self._tool_methods.get(name)

    def list_tool_methods(self) -> list[str]:
        """Return a list of all available tool method names."""
        return list(self._tool_methods.keys())


def tool_method(name: Optional[str] = None, description: Optional[str] = None):
    """
    Decorator to mark a method as a tool method.

    Args:
        name: Optional custom name for the tool method
        description: Optional description for the tool method
    """

    def decorator(func: Callable):
        func._is_tool_method = True
        func._tool_name = name or func.__name__
        func._tool_description = description or func.__doc__

        @wraps(func)
        async def wrapper(*args, **kwargs):
            return await func(*args, **kwargs)

        # Copy the tool metadata to the wrapper
        wrapper._is_tool_method = True
        wrapper._tool_name = func._tool_name
        wrapper._tool_description = func._tool_description

        return wrapper

    return decorator


class ToolRegistry:
    """
    Registry for managing tool instances.

    This class provides a centralized way to register and manage tool instances.
    It's designed to be used internally and not exposed to external users.
    """

    def __init__(self):
        self._tools: dict[str, BaseTool] = {}
        self._tool_methods: dict[str, Callable] = {}

    def _register_tool_instance(self, tool_instance: BaseTool):
        """
        Internal method to register a tool instance.

        This method is prefixed with underscore to indicate it's for internal use.

        Args:
            tool_instance: An instance of a class that inherits from BaseTool
        """
        if not isinstance(tool_instance, BaseTool):
            raise TypeError(
                f"Tool must inherit from BaseTool, got {type(tool_instance)}"
            )

        tool_name = tool_instance.tool_name

        if tool_name in self._tools:
            raise ValueError(f"Tool '{tool_name}' is already registered")

        self._tools[tool_name] = tool_instance

        # Register all tool methods from this instance
        for method_name, method in tool_instance.get_tool_methods().items():
            full_method_name = f"{tool_name}.{method_name}"
            self._tool_methods[full_method_name] = method

    def register_tool_class(self, tool_class: type):
        """
        Public method to register a tool class.

        This instantiates the tool class and registers it.

        Args:
            tool_class: A class that inherits from BaseTool
        """
        if not issubclass(tool_class, BaseTool):
            raise TypeError(f"Tool class must inherit from BaseTool, got {tool_class}")

        tool_instance = tool_class()
        self._register_tool_instance(tool_instance)

    def get_tool(self, name: str) -> Optional[BaseTool]:
        """Get a tool instance by name."""
        return self._tools.get(name)

    def get_tool_method(self, name: str) -> Optional[Callable]:
        """Get a tool method by name."""
        return self._tool_methods.get(name)

    def list_tools(self) -> list[str]:
        """Return a list of all registered tool names."""
        return list(self._tools.keys())

    def list_tool_methods(self) -> list[str]:
        """Return a list of all registered tool method names."""
        return list(self._tool_methods.keys())

    def get_all_tool_methods(self) -> dict[str, Callable]:
        """Return all registered tool methods."""
        return self._tool_methods.copy()


# Global registry instance
_global_registry = ToolRegistry()


def get_tool_registry() -> ToolRegistry:
    """Get the global tool registry instance."""
    return _global_registry


def register_tool_class(tool_class: type):
    """
    Convenience function to register a tool class with the global registry.

    Args:
        tool_class: A class that inherits from BaseTool
    """
    _global_registry.register_tool_class(tool_class)
