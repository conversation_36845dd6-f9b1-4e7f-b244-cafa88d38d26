"""
script_tool.py

Script execution tool with safety constraints and predefined scripts.

Author: <PERSON>
"""
import asyncio
import os
import tempfile

from .base_tool import BaseTool, tool_method

# Script runner functionality
PREDEFINED_SCRIPTS = {
    "system_info": {
        "description": "Get system information",
        "script": "python3 -c \"import platform, psutil; print(f'OS: {platform.system()} {platform.release()}'); print(f'CPU: {psutil.cpu_percent()}% usage'); print(f'Memory: {psutil.virtual_memory().percent}% usage')\"",
    },
    "disk_usage": {"description": "Check disk usage", "script": "df -h"},
    "weather_test": {
        "description": "Test weather API connectivity",
        "script": "python3 -c \"import requests; r = requests.get('https://api.weather.gov/alerts/active/area/CA', timeout=10); print(f'Status: {r.status_code}'); print(f'Response size: {len(r.text)} bytes')\"",
    },
    "network_test": {
        "description": "Test network connectivity",
        "script": "ping -c 3 *******",
    },
    "date_time": {"description": "Get current date and time", "script": "date"},
}


async def run_script_safely(script: str, timeout: int = 30) -> dict:
    """
    Run a script safely with timeout and error handling.

    Args:
        script: The script command to execute
        timeout: Maximum execution time in seconds

    Returns:
        Dict with success status, output, and error information
    """
    try:
        # Run the script with timeout
        process = await asyncio.create_subprocess_shell(
            script,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            limit=1024 * 1024,  # 1MB limit for output
        )

        try:
            stdout, stderr = await asyncio.wait_for(
                process.communicate(), timeout=timeout
            )

            return {
                "success": True,
                "return_code": process.returncode,
                "stdout": stdout.decode("utf-8", errors="replace"),
                "stderr": stderr.decode("utf-8", errors="replace"),
                "timeout": False,
            }

        except asyncio.TimeoutError:
            # Kill the process if it times out
            process.kill()
            await process.wait()
            return {
                "success": False,
                "return_code": -1,
                "stdout": "",
                "stderr": f"Script execution timed out after {timeout} seconds",
                "timeout": True,
            }

    except Exception as e:
        return {
            "success": False,
            "return_code": -1,
            "stdout": "",
            "stderr": f"Failed to execute script: {str(e)}",
            "timeout": False,
        }


class ScriptTool(BaseTool):
    """Tool for executing scripts safely with predefined and custom script support."""

    @property
    def tool_name(self) -> str:
        return "script_tool"

    @property
    def tool_description(self) -> str:
        return "Execute scripts safely with timeout and safety constraints"

    @tool_method(name="run_predefined_script")
    async def run_predefined_script(self, script_name: str, timeout: int = 30) -> str:
        """Run a predefined script by name.

        Args:
            script_name: Name of the predefined script to run
            timeout: Maximum execution time in seconds (default: 30)
        """
        if script_name not in PREDEFINED_SCRIPTS:
            available_scripts = list(PREDEFINED_SCRIPTS.keys())
            return f"Script '{script_name}' not found. Available scripts: {', '.join(available_scripts)}"

        script_info = PREDEFINED_SCRIPTS[script_name]
        script_command = script_info["script"]
        description = script_info["description"]

        result = await run_script_safely(script_command, timeout)

        output = f"Script: {script_name}\nDescription: {description}\n"
        output += f"Return Code: {result['return_code']}\n"

        if result["success"]:
            output += f"Status: ✅ Success\n"
            if result["stdout"]:
                output += f"\nOutput:\n{result['stdout']}"
            if result["stderr"]:
                output += f"\nWarnings/Errors:\n{result['stderr']}"
        else:
            output += f"Status: ❌ Failed\n"
            if result["timeout"]:
                output += f"⏰ Script timed out after {timeout} seconds\n"
            output += f"Error: {result['stderr']}"

        return output

    @tool_method(name="run_custom_script")
    async def run_custom_script(
        self, script_content: str, language: str = "bash", timeout: int = 30
    ) -> str:
        """Run a custom script with safety constraints.

        Args:
            script_content: The script content to execute
            language: Script language (bash, python3, node) - default: bash
            timeout: Maximum execution time in seconds (default: 30)
        """
        # Safety checks
        dangerous_commands = [
            "rm -rf",
            "rm -f /",
            "mkfs",
            "dd if=",
            "format",
            "fdisk",
            "sudo rm",
            "sudo dd",
            "chmod 777",
            "> /dev/",
            "curl.*|.*sh",
            "wget.*|.*sh",
            "eval",
            "exec",
            "system(",
            "os.system",
            "subprocess.call",
            "__import__",
            "compile",
        ]

        script_lower = script_content.lower()
        for dangerous in dangerous_commands:
            if dangerous in script_lower:
                return f"❌ Script rejected: Contains potentially dangerous command '{dangerous}'"

        # Limit script size
        if len(script_content) > 10000:  # 10KB limit
            return "❌ Script rejected: Script too large (max 10KB)"

        # Create temporary file for the script
        try:
            with tempfile.NamedTemporaryFile(
                mode="w", suffix=f".{language}", delete=False
            ) as tmp_file:
                tmp_file.write(script_content)
                tmp_file_path = tmp_file.name

            # Determine how to execute the script
            if language == "python3":
                command = f"python3 {tmp_file_path}"
            elif language == "node":
                command = f"node {tmp_file_path}"
            elif language == "bash":
                command = f"bash {tmp_file_path}"
            else:
                return f"❌ Unsupported language: {language}. Supported: bash, python3, node"

            result = await run_script_safely(command, timeout)

            # Clean up temporary file
            try:
                os.unlink(tmp_file_path)
            except:
                pass

            output = f"Custom Script ({language})\n"
            output += f"Return Code: {result['return_code']}\n"

            if result["success"]:
                output += f"Status: ✅ Success\n"
                if result["stdout"]:
                    output += f"\nOutput:\n{result['stdout']}"
                if result["stderr"]:
                    output += f"\nWarnings/Errors:\n{result['stderr']}"
            else:
                output += f"Status: ❌ Failed\n"
                if result["timeout"]:
                    output += f"⏰ Script timed out after {timeout} seconds\n"
                output += f"Error: {result['stderr']}"

            return output

        except Exception as e:
            return f"❌ Failed to create/execute script: {str(e)}"

    @tool_method(name="list_predefined_scripts")
    async def list_predefined_scripts(self) -> str:
        """List all available predefined scripts."""
        if not PREDEFINED_SCRIPTS:
            return "No predefined scripts available."

        output = "Available Predefined Scripts:\n\n"
        for name, info in PREDEFINED_SCRIPTS.items():
            output += f"• {name}\n"
            output += f"  Description: {info['description']}\n"
            output += f"  Command: {info['script']}\n\n"

        return output
